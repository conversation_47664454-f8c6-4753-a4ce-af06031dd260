/*
 * @Author: YuanW<PERSON> <EMAIL>
 * @Date: 2025-07-18 12:03:16
 * @LastEditors: YuanWei <EMAIL>
 * @LastEditTime: 2025-07-18 12:03:53
 * @FilePath: \db-manage\dbmanage-boot\db-admin\src\main\java\com\db\web\controller\tool\HotReloadTestController.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.db.web.controller.tool;

import com.db.common.core.controller.BaseController;
import com.db.common.core.domain.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 热加载测试控制器
 * 用于验证Spring Boot DevTools热加载功能
 *
 * <AUTHOR>
 */
@Tag(name = "热加载测试")
@RestController
@RequestMapping("/test/hot-reload")
public class HotReloadTestController extends BaseController {

    private static int counter = 0;
    private static String lastModified = LocalDateTime.now().toString();

    @Operation(summary = "获取热加载测试信息")
    @GetMapping("/info")
    public R<Map<String, Object>> getHotReloadInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("counter", ++counter);
        info.put("lastModified", lastModified);
        info.put("message", "热加载测试成功！修改这个控制器代码后保存，应用会自动重启。");
        info.put("timestamp", LocalDateTime.now().toString());
        info.put("version", "1.0.0");

        return R.ok(info);
    }

    @Operation(summary = "重置计数器")
    @GetMapping("/reset")
    public R<Map<String, Object>> resetCounter() {
        counter = 0;
        lastModified = LocalDateTime.now().toString();

        Map<String, Object> result = new HashMap<>();
        result.put("message", "计数器已重置");
        result.put("counter", counter);
        result.put("lastModified", lastModified);

        return R.ok(result);
    }

    @Operation(summary = "获取当前状态")
    @GetMapping("/status")
    public R<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("counter", counter);
        status.put("lastModified", lastModified);
        status.put("hotReloadEnabled", true);
        status.put("devToolsActive", true);

        return R.ok(status);
    }
}
<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="mysql">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root||ALTER|G
|root||root|localhost|ALTER|G
|root||root||ALTER ROUTINE|G
|root||root|localhost|ALTER ROUTINE|G
|root||root||CREATE|G
|root||root|localhost|CREATE|G
|root||root||CREATE ROUTINE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root||CREATE TABLESPACE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root||CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root||CREATE USER|G
|root||root|localhost|CREATE USER|G
|root||root||CREATE VIEW|G
|root||root|localhost|CREATE VIEW|G
|root||root||DELETE|G
|root||root|localhost|DELETE|G
|root||root||DROP|G
|root||root|localhost|DROP|G
|root||root||EVENT|G
|root||root|localhost|EVENT|G
|root||root||EXECUTE|G
|root||root|localhost|EXECUTE|G
|root||root||FILE|G
|root||root|localhost|FILE|G
|root||root||INDEX|G
|root||root|localhost|INDEX|G
|root||root||INSERT|G
|root||root|localhost|INSERT|G
|root||root||LOCK TABLES|G
|root||root|localhost|LOCK TABLES|G
|root||root||PROCESS|G
|root||root|localhost|PROCESS|G
|root||root||REFERENCES|G
|root||root|localhost|REFERENCES|G
|root||root||RELOAD|G
|root||root|localhost|RELOAD|G
|root||root||REPLICATION CLIENT|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root||REPLICATION SLAVE|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root||SELECT|G
|root||root|localhost|SELECT|G
|root||root||SHOW DATABASES|G
|root||root|localhost|SHOW DATABASES|G
|root||root||SHOW VIEW|G
|root||root|localhost|SHOW VIEW|G
|root||root||SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root||SUPER|G
|root||root|localhost|SUPER|G
|root||root||TRIGGER|G
|root||root|localhost|TRIGGER|G
|root||root||UPDATE|G
|root||root|localhost|UPDATE|G
|root||root||grant option|G
|root||root|localhost|grant option|G
lwkj\\_tts|schema||teach||ALTER|G
lwkj\\_tts|schema||teach||CREATE|G
lwkj\\_tts|schema||teach||CREATE ROUTINE|G
lwkj\\_tts|schema||teach||CREATE TEMPORARY TABLES|G
lwkj\\_tts|schema||teach||CREATE VIEW|G
lwkj\\_tts|schema||teach||DELETE|G
lwkj\\_tts|schema||teach||INDEX|G
lwkj\\_tts|schema||teach||INSERT|G
lwkj\\_tts|schema||teach||SELECT|G
lwkj\\_tts|schema||teach||TRIGGER|G
lwkj\\_tts|schema||teach||UPDATE|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G
teach|schema||teach||ALTER|G
teach|schema||teach||CREATE|G
teach|schema||teach||CREATE ROUTINE|G
teach|schema||teach||CREATE TEMPORARY TABLES|G
teach|schema||teach||CREATE VIEW|G
teach|schema||teach||DELETE|G
teach|schema||teach||INDEX|G
teach|schema||teach||INSERT|G
teach|schema||teach||SELECT|G
teach|schema||teach||TRIGGER|G
teach|schema||teach||UPDATE|G</Grants>
      <ServerVersion>5.7.40</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="224" parent="1" name="bf_evaluate">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="bladex_boot">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="cms_2021">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="db-manage">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="228" parent="1" name="db-manage1">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-07-17.02:55:28</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="229" parent="1" name="dbever-nacos">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="230" parent="1" name="hc_community">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="231" parent="1" name="hcp">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="232" parent="1" name="hn">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="233" parent="1" name="hnnew">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="234" parent="1" name="hycg_kysj">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="235" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="236" parent="1" name="kingbase-nacos">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="237" parent="1" name="lwkj_tts">
      <CollationName>gbk_chinese_ci</CollationName>
    </schema>
    <schema id="238" parent="1" name="mysql">
      <CollationName>latin1_swedish_ci</CollationName>
    </schema>
    <schema id="239" parent="1" name="mzcp_bladex_boot">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="240" parent="1" name="nybz">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="241" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="242" parent="1" name="qh-kysjgl">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="243" parent="1" name="qh_szhzc">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="244" parent="1" name="ruoyi-vue-pro">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="245" parent="1" name="rw_emergency">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="246" parent="1" name="ry">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="247" parent="1" name="ry-config">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="248" parent="1" name="ry-dbmanage">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="249" parent="1" name="ry-nacos">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="250" parent="1" name="ry-seata">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="251" parent="1" name="ry-vue">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="252" parent="1" name="ry-vue-kms">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="253" parent="1" name="ry-vue-test">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="254" parent="1" name="ry_safety_risk_assessment">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="255" parent="1" name="safety_risk_assessment">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="256" parent="1" name="seize_orders">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="257" parent="1" name="simulation0823">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="258" parent="1" name="siwu_iot_2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="259" parent="1" name="sys">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="260" parent="1" name="teach">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="261" parent="1" name="test-jl">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="262" parent="1" name="tt">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="263" parent="1" name="webcat-1.0">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="264" parent="1" name="webcat-1.2.0">
      <CollationName>utf8_unicode_ci</CollationName>
    </schema>
    <schema id="265" parent="1" name="wq">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="266" parent="1" name="wr">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="267" parent="1" name="wvp2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="268" parent="1" name="xjxl_job">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="269" parent="1" name="xuejibaio">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="270" parent="1" name="xuejixitong">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="271" parent="1" name="xxl_job">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="272" parent="1" name="yj">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="273" parent="1" name="yj-update">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="274" parent="1" name="yj-update-new">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="275" parent="1" name="yj_update">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="276" parent="1" name="yshopb2c">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="277" parent="1" name="ywksh">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <schema id="278" parent="1" name="zas">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="279" parent="1" name="zjl-nacos">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="280" parent="1" name="zjl-nacos2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="281" parent="1" name="zopen-base">
      <CollationName>utf8_bin</CollationName>
    </schema>
    <user id="282" parent="1" name="mysql.session">
      <Host>localhost</Host>
    </user>
    <user id="283" parent="1" name="mysql.sys">
      <Host>localhost</Host>
    </user>
    <user id="284" parent="1" name="root"/>
    <user id="285" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <user id="286" parent="1" name="teach"/>
    <table id="287" parent="228" name="db_chart">
      <Comment>自定义报表表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="288" parent="228" name="db_dashboard">
      <Comment>自定义报表表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="289" parent="228" name="db_dashboard_chart_relation">
      <Comment>自定义报表表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="290" parent="228" name="db_data_publication">
      <Comment>数据发布表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="291" parent="228" name="db_data_source">
      <Comment>数据源连接表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="292" parent="228" name="db_data_source_access">
      <Comment>数据源授权</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="293" parent="228" name="db_data_subscription">
      <Comment>数据订阅表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="294" parent="228" name="db_environment">
      <Comment>数据库连接环境</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="295" parent="228" name="db_erd_graph">
      <Comment>我的ER图模型</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="296" parent="228" name="db_jdbc_driver">
      <Comment>自定义驱动表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="297" parent="228" name="db_operation_log">
      <Comment>我的执行记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="298" parent="228" name="db_operation_saved">
      <Comment>我的保存表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="299" parent="228" name="db_pin_table">
      <Comment>PIN TABLES</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="300" parent="228" name="db_system_config">
      <Comment>配置中心表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="301" parent="228" name="db_table_cache">
      <Comment>table cache</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="302" parent="228" name="db_table_cache_version">
      <Comment>table cache version</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="303" parent="228" name="db_table_vector_mapping">
      <Comment>milvus映射表保存记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="304" parent="228" name="db_task">
      <Comment>数据导出导入任务</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="305" parent="228" name="db_team">
      <Comment>团队</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="306" parent="228" name="db_team_user">
      <Comment>用户团队表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="307" parent="228" name="db_transaction_session">
      <Comment>事务会话表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="308" parent="228" name="db_transaction_sql_operation">
      <Comment>SQL操作记录表 </Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="309" parent="228" name="flyway_schema_history">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="310" parent="228" name="gen_table">
      <Comment>代码生成业务表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="311" parent="228" name="gen_table_column">
      <Comment>代码生成业务表字段</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="312" parent="228" name="qrtz_blob_triggers">
      <Comment>Blob类型的触发器表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="313" parent="228" name="qrtz_calendars">
      <Comment>日历信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="314" parent="228" name="qrtz_cron_triggers">
      <Comment>Cron类型的触发器表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="315" parent="228" name="qrtz_fired_triggers">
      <Comment>已触发的触发器表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="316" parent="228" name="qrtz_job_details">
      <Comment>任务详细信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="317" parent="228" name="qrtz_locks">
      <Comment>存储的悲观锁信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="318" parent="228" name="qrtz_paused_trigger_grps">
      <Comment>暂停的触发器表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="319" parent="228" name="qrtz_scheduler_state">
      <Comment>调度器状态表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="320" parent="228" name="qrtz_simple_triggers">
      <Comment>简单触发器的信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="321" parent="228" name="qrtz_simprop_triggers">
      <Comment>同步机制的行锁表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="322" parent="228" name="qrtz_triggers">
      <Comment>触发器详细信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="323" parent="228" name="sys_config">
      <Comment>参数配置表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="324" parent="228" name="sys_dept">
      <Comment>部门表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="325" parent="228" name="sys_dict_data">
      <Comment>字典数据表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="326" parent="228" name="sys_dict_type">
      <Comment>字典类型表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="327" parent="228" name="sys_job">
      <Comment>定时任务调度表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="328" parent="228" name="sys_job_log">
      <Comment>定时任务调度日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="329" parent="228" name="sys_logininfor">
      <Comment>系统访问记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="330" parent="228" name="sys_menu">
      <Comment>菜单权限表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="331" parent="228" name="sys_notice">
      <Comment>通知公告表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="332" parent="228" name="sys_oper_log">
      <Comment>操作日志记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="333" parent="228" name="sys_post">
      <Comment>岗位信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="334" parent="228" name="sys_role">
      <Comment>角色信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="335" parent="228" name="sys_role_dept">
      <Comment>角色和部门关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="336" parent="228" name="sys_role_menu">
      <Comment>角色和菜单关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="337" parent="228" name="sys_user">
      <Comment>用户信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="338" parent="228" name="sys_user_post">
      <Comment>用户与岗位关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="339" parent="228" name="sys_user_role">
      <Comment>用户和角色关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <column id="340" parent="287" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="341" parent="287" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="342" parent="287" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="343" parent="287" name="name">
      <Comment>图表名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="344" parent="287" name="description">
      <Comment>图表描述</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="345" parent="287" name="schema">
      <Comment>图表信息</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="346" parent="287" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <Position>7</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="347" parent="287" name="type">
      <Comment>数据库类型</Comment>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="348" parent="287" name="database_name">
      <Comment>db名称</Comment>
      <Position>9</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="349" parent="287" name="ddl">
      <Comment>ddl内容</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="350" parent="287" name="deleted">
      <Comment>是否被删除,y表示删除,n表示未删除</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="351" parent="287" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="352" parent="287" name="schema_name">
      <Comment>schemaName</Comment>
      <Position>13</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <index id="353" parent="287" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="354" parent="287" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="355" parent="288" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="356" parent="288" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="357" parent="288" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="358" parent="288" name="name">
      <Comment>报表名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="359" parent="288" name="description">
      <Comment>报表描述</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="360" parent="288" name="schema">
      <Comment>报表布局信息</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="361" parent="288" name="deleted">
      <Comment>是否被删除,y表示删除,n表示未删除</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="362" parent="288" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <index id="363" parent="288" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="364" parent="288" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="365" parent="289" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="366" parent="289" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="367" parent="289" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="368" parent="289" name="dashboard_id">
      <Comment>报表id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="369" parent="289" name="chart_id">
      <Comment>图表id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <index id="370" parent="289" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="371" parent="289" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="372" parent="290" name="id">
      <AutoIncrement>22</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="373" parent="290" name="name">
      <Comment>发布名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="374" parent="290" name="description">
      <Comment>描述</Comment>
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="375" parent="290" name="data_source_name">
      <Comment>连接名</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="376" parent="290" name="database_type">
      <Comment>数据库类型</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="377" parent="290" name="database_name">
      <Comment>数据库名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="378" parent="290" name="schema_name">
      <Comment>模式名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="379" parent="290" name="publication_type">
      <Comment>同步对象 table：表，procedure：存储过程，function：函数</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="380" parent="290" name="object_name">
      <Comment>对象名称</Comment>
      <Position>9</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="381" parent="290" name="structure_topic_name">
      <Comment>结构主题名称</Comment>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="382" parent="290" name="data_topic_name">
      <Comment>数据主题名称</Comment>
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="383" parent="290" name="create_by">
      <Comment>发布人id</Comment>
      <Position>12</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="384" parent="290" name="create_by_name">
      <Comment>发布人姓名</Comment>
      <Position>13</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="385" parent="290" name="create_time">
      <Comment>发布时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="386" parent="290" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="387" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="388" parent="291" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="389" parent="291" name="gmt_create">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="390" parent="291" name="gmt_modified">
      <Comment>修改时间</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="391" parent="291" name="alias">
      <Comment>别名</Comment>
      <Position>4</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="392" parent="291" name="url">
      <Comment>连接地址</Comment>
      <Position>5</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="393" parent="291" name="user_name">
      <Comment>用户名</Comment>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="394" parent="291" name="password">
      <Comment>密码</Comment>
      <Position>7</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="395" parent="291" name="type">
      <Comment>数据库类型</Comment>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="396" parent="291" name="env_type">
      <Comment>环境类型</Comment>
      <Position>9</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="397" parent="291" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="398" parent="291" name="host">
      <Comment>host地址</Comment>
      <Position>11</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="399" parent="291" name="port">
      <Comment>端口</Comment>
      <Position>12</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="400" parent="291" name="ssh">
      <Comment>ssh配置信息json</Comment>
      <Position>13</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="401" parent="291" name="ssl">
      <Comment>ssl配置信息json</Comment>
      <Position>14</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="402" parent="291" name="sid">
      <Comment>sid</Comment>
      <Position>15</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="403" parent="291" name="driver">
      <Comment>驱动信息</Comment>
      <Position>16</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="404" parent="291" name="jdbc">
      <Comment>jdbc版本</Comment>
      <Position>17</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="405" parent="291" name="extend_info">
      <Comment>自定义扩展字段json</Comment>
      <Position>18</Position>
      <StoredType>varchar(4096)|0s</StoredType>
    </column>
    <column id="406" parent="291" name="driver_config">
      <Comment>driver_config配置</Comment>
      <Position>19</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="407" parent="291" name="environment_id">
      <Comment>环境id</Comment>
      <DefaultExpression>2</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="408" parent="291" name="kind">
      <Comment>连接类型</Comment>
      <DefaultExpression>&apos;PRIVATE&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="409" parent="291" name="service_name">
      <Comment>服务名</Comment>
      <Position>22</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="410" parent="291" name="service_type">
      <Comment>服务类型</Comment>
      <Position>23</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <index id="411" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="412" parent="291" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="413" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="414" parent="292" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="415" parent="292" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="416" parent="292" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="417" parent="292" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="418" parent="292" name="modified_user_id">
      <Comment>修改人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="419" parent="292" name="data_source_id">
      <Comment>数据源id</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="420" parent="292" name="access_object_type">
      <Comment>授权类型</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="421" parent="292" name="access_object_id">
      <Comment>授权id,根据类型区分是用户还是团队</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <index id="422" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="423" parent="292" name="uk_data_source_access">
      <ColNames>data_source_id
access_object_type
access_object_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="424" parent="292" name="idx_data_source_access_data_source_id">
      <ColNames>data_source_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="425" parent="292" name="idx_data_source_access_access_object_id">
      <ColNames>access_object_type
access_object_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="426" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="427" parent="292" name="uk_data_source_access">
      <UnderlyingIndexName>uk_data_source_access</UnderlyingIndexName>
    </key>
    <column id="428" parent="293" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="429" parent="293" name="publication_id">
      <Comment>发布ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="430" parent="293" name="name">
      <Comment>订阅名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="431" parent="293" name="target_datasource_name">
      <Comment>目标连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="432" parent="293" name="target_datasource_display_name">
      <Comment>目标连接名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="433" parent="293" name="target_database_type">
      <Comment>目标数据库类型</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="434" parent="293" name="target_database_name">
      <Comment>目标数据库名称</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="435" parent="293" name="syn_status">
      <Comment>0：订阅失败 1: 订阅成功</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="436" parent="293" name="status">
      <Comment>0：同步失败 1:同步中 2：同步成功</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="437" parent="293" name="syn_error_msg">
      <Comment>同步错误日志</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="438" parent="293" name="create_by">
      <Comment>创建者</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="439" parent="293" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="440" parent="293" name="update_by">
      <Comment>更新者</Comment>
      <Position>13</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="441" parent="293" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="442" parent="293" name="remark">
      <Comment>备注</Comment>
      <Position>15</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="443" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="444" parent="293" name="idx_publication_id">
      <ColNames>publication_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="445" parent="293" name="idx_subscription_composite">
      <ColNames>target_datasource_name
target_database_name
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="446" parent="293" name="idx_target_datasource">
      <ColNames>target_datasource_name</ColNames>
      <Type>btree</Type>
    </index>
    <index id="447" parent="293" name="idx_syn_status">
      <ColNames>syn_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="448" parent="293" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="449" parent="293" name="idx_create_by">
      <ColNames>create_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="450" parent="293" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="451" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="452" parent="294" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="453" parent="294" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="454" parent="294" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="455" parent="294" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="456" parent="294" name="modified_user_id">
      <Comment>修改人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="457" parent="294" name="name">
      <Comment>环境名称</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="458" parent="294" name="short_name">
      <Comment>环境缩写</Comment>
      <Position>7</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="459" parent="294" name="color">
      <Comment>颜色</Comment>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <index id="460" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="461" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="462" parent="295" name="id">
      <AutoIncrement>131</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="463" parent="295" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="464" parent="295" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="465" parent="295" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="466" parent="295" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="467" parent="295" name="name">
      <Comment>保存名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="468" parent="295" name="type">
      <Comment>数据库类型</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="469" parent="295" name="status">
      <Comment>ddl语句状态:DRAFT/RELEASE</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="470" parent="295" name="erd_json">
      <Comment>ddl内容</Comment>
      <Position>9</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="471" parent="295" name="tab_opened">
      <Comment>是否在tab中被打开,y表示打开,n表示未打开</Comment>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="472" parent="295" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="473" parent="295" name="db_schema_name">
      <Comment>schema名称</Comment>
      <Position>12</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="474" parent="295" name="operation_type">
      <Comment>操作类型</Comment>
      <Position>13</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <index id="475" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="476" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="477" parent="296" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="478" parent="296" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="479" parent="296" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="480" parent="296" name="db_type">
      <Comment>db类型</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="481" parent="296" name="jdbc_driver">
      <Comment>jar包</Comment>
      <Position>5</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="482" parent="296" name="jdbc_driver_class">
      <Comment>driver class类</Comment>
      <Position>6</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <index id="483" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="484" parent="296" name="idx_db_type">
      <ColNames>db_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="485" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="486" parent="297" name="id">
      <AutoIncrement>917</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="487" parent="297" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="488" parent="297" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="489" parent="297" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="490" parent="297" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="491" parent="297" name="type">
      <Comment>数据库类型</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="492" parent="297" name="ddl">
      <Comment>ddl内容</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="493" parent="297" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="494" parent="297" name="status">
      <Comment>状态</Comment>
      <DefaultExpression>&apos;success&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="495" parent="297" name="operation_rows">
      <Comment>操作行数</Comment>
      <Position>10</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="496" parent="297" name="use_time">
      <Comment>使用时长</Comment>
      <Position>11</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="497" parent="297" name="extend_info">
      <Comment>扩展信息</Comment>
      <Position>12</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="498" parent="297" name="schema_name">
      <Comment>schema名称</Comment>
      <Position>13</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <index id="499" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="500" parent="297" name="idx_op_data_source_id">
      <ColNames>data_source_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="501" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="502" parent="298" name="id">
      <AutoIncrement>151</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="503" parent="298" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="504" parent="298" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="505" parent="298" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="506" parent="298" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="507" parent="298" name="name">
      <Comment>保存名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="508" parent="298" name="type">
      <Comment>数据库类型</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="509" parent="298" name="status">
      <Comment>ddl语句状态:DRAFT/RELEASE</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="510" parent="298" name="ddl">
      <Comment>ddl内容</Comment>
      <Position>9</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="511" parent="298" name="tab_opened">
      <Comment>是否在tab中被打开,y表示打开,n表示未打开</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="512" parent="298" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="513" parent="298" name="db_schema_name">
      <Comment>schema名称</Comment>
      <Position>12</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="514" parent="298" name="operation_type">
      <Comment>操作类型</Comment>
      <Position>13</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <index id="515" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="516" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="517" parent="299" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="518" parent="299" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="519" parent="299" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="520" parent="299" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="521" parent="299" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="522" parent="299" name="schema_name">
      <Comment>schema名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="523" parent="299" name="table_name">
      <Comment>table_name</Comment>
      <Position>7</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="524" parent="299" name="deleted">
      <Comment>是否被删除,y表示删除,n表示未删除</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="525" parent="299" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <index id="526" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="527" parent="299" name="idx_user_id_data_source_id">
      <ColNames>user_id
data_source_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="528" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="529" parent="300" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="530" parent="300" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="531" parent="300" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="532" parent="300" name="code">
      <Comment>配置项编码</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="533" parent="300" name="content">
      <Comment>配置项内容</Comment>
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="534" parent="300" name="summary">
      <Comment>配置项说明</Comment>
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <index id="535" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="536" parent="300" name="uk_code">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="537" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="538" parent="300" name="uk_code">
      <UnderlyingIndexName>uk_code</UnderlyingIndexName>
    </key>
    <column id="539" parent="301" name="id">
      <AutoIncrement>7079</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="540" parent="301" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="541" parent="301" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="542" parent="301" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="543" parent="301" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="544" parent="301" name="schema_name">
      <Comment>schema名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="545" parent="301" name="table_name">
      <Comment>table名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="546" parent="301" name="key">
      <Comment>唯一索引</Comment>
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="547" parent="301" name="version">
      <Comment>版本</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="548" parent="301" name="columns">
      <Comment>表字段</Comment>
      <Position>10</Position>
      <StoredType>varchar(2048)|0s</StoredType>
    </column>
    <column id="549" parent="301" name="extend_info">
      <Comment>自定义扩展字段json</Comment>
      <Position>11</Position>
      <StoredType>varchar(2048)|0s</StoredType>
    </column>
    <index id="550" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="551" parent="301" name="idx_table_cache_data_source_id">
      <ColNames>data_source_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="552" parent="301" name="idx_table_cache_key_table_name">
      <ColNames>key
table_name</ColNames>
      <Type>btree</Type>
    </index>
    <index id="553" parent="301" name="idx_table_cache_key_version">
      <ColNames>key
version</ColNames>
      <Type>btree</Type>
    </index>
    <key id="554" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="555" parent="302" name="id">
      <AutoIncrement>54</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="556" parent="302" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="557" parent="302" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="558" parent="302" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="559" parent="302" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="560" parent="302" name="schema_name">
      <Comment>schema名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="561" parent="302" name="key">
      <Comment>唯一索引</Comment>
      <Position>7</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="562" parent="302" name="version">
      <Comment>版本</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="563" parent="302" name="table_count">
      <Comment>表数量</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="564" parent="302" name="status">
      <Comment>状态</Comment>
      <Position>10</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <index id="565" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="566" parent="302" name="uk_table_cache_version_key">
      <ColNames>key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="567" parent="302" name="idx_table_cache_version_data_source_id">
      <ColNames>data_source_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="568" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="569" parent="302" name="uk_table_cache_version_key">
      <UnderlyingIndexName>uk_table_cache_version_key</UnderlyingIndexName>
    </key>
    <column id="570" parent="303" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="571" parent="303" name="api_key">
      <Comment>api key</Comment>
      <Position>2</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="572" parent="303" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <Position>3</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="573" parent="303" name="database">
      <Comment>数据库名称</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="574" parent="303" name="schema">
      <Comment>schema名称</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="575" parent="303" name="status">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <index id="576" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="577" parent="303" name="idx_api_key">
      <ColNames>api_key</ColNames>
      <Type>btree</Type>
    </index>
    <key id="578" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="579" parent="304" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="580" parent="304" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="581" parent="304" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="582" parent="304" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="583" parent="304" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="584" parent="304" name="schema_name">
      <Comment>schema名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="585" parent="304" name="table_name">
      <Comment>table_name</Comment>
      <Position>7</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="586" parent="304" name="deleted">
      <Comment>是否被删除,y表示删除,n表示未删除</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="587" parent="304" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="588" parent="304" name="task_type">
      <Comment>task type, such as: DOWNLOAD_DATA, UPLOAD_TABLE_DATA, DOWNLOAD_TABLE_STRUCTURE, UPLOAD_TABLE_STRUCTURE,</Comment>
      <Position>10</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="589" parent="304" name="task_status">
      <Comment>task status</Comment>
      <Position>11</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="590" parent="304" name="task_progress">
      <Comment>task progress</Comment>
      <Position>12</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="591" parent="304" name="task_name">
      <Comment>task name</Comment>
      <Position>13</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="592" parent="304" name="content">
      <Comment>task content</Comment>
      <Position>14</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="593" parent="304" name="download_url">
      <Comment>down load url</Comment>
      <Position>15</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <index id="594" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="595" parent="304" name="idx_task_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="596" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="597" parent="305" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="598" parent="305" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="599" parent="305" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="600" parent="305" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="601" parent="305" name="modified_user_id">
      <Comment>修改人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="602" parent="305" name="code">
      <Comment>团队编码</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="603" parent="305" name="name">
      <Comment>团队名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="604" parent="305" name="status">
      <Comment>团队状态</Comment>
      <DefaultExpression>&apos;VALID&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="605" parent="305" name="description">
      <Comment>团队描述</Comment>
      <Position>9</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="606" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="607" parent="305" name="uk_team_code">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="608" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="609" parent="305" name="uk_team_code">
      <UnderlyingIndexName>uk_team_code</UnderlyingIndexName>
    </key>
    <column id="610" parent="306" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="611" parent="306" name="gmt_create">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="612" parent="306" name="gmt_modified">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="613" parent="306" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="614" parent="306" name="modified_user_id">
      <Comment>修改人用户id</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="615" parent="306" name="team_id">
      <Comment>团队id</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="616" parent="306" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <index id="617" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="618" parent="306" name="uk_team_user">
      <ColNames>team_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="619" parent="306" name="idx_team_user_team_id">
      <ColNames>team_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="620" parent="306" name="idx_team_user_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="621" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="622" parent="306" name="uk_team_user">
      <UnderlyingIndexName>uk_team_user</UnderlyingIndexName>
    </key>
    <column id="623" parent="307" name="id">
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="624" parent="307" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="625" parent="307" name="update_time">
      <Comment>修改时间</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="626" parent="307" name="data_source_id">
      <Comment>数据源连接ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="627" parent="307" name="database_name">
      <Comment>db名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="628" parent="307" name="db_schema_name">
      <Comment>schema名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="629" parent="307" name="type">
      <Comment>数据库类型</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="630" parent="307" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint(20) unsigned|0s</StoredType>
    </column>
    <column id="631" parent="307" name="start_time">
      <Comment>事务开始时间</Comment>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="632" parent="307" name="end_time">
      <Comment>事务结束时间</Comment>
      <Position>10</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="633" parent="307" name="isolation_level">
      <Comment>事务隔离级别（如READ_COMMITTED）</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="634" parent="307" name="status">
      <Comment>状态（0:进行中,1:已提交,2:回滚）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="635" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="636" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="637" parent="308" name="id">
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="638" parent="308" name="session_id">
      <Comment>事务会话ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="639" parent="308" name="sql_text">
      <Comment>执行的SQL语句</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="640" parent="308" name="operation_type">
      <Comment>操作类型（SELECT, INSERT, UPDATE, DELETE）</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="641" parent="308" name="executed_at">
      <Comment>执行时间</Comment>
      <Position>5</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="642" parent="308" name="affected_rows">
      <Comment>影响行数</Comment>
      <Position>6</Position>
      <StoredType>int(255)|0s</StoredType>
    </column>
    <column id="643" parent="308" name="success">
      <Comment>是否执行成功 0否，1是</Comment>
      <Position>7</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="644" parent="308" name="result_msg">
      <Comment>错误信息（失败时记录）</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="645" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="646" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="647" parent="309" name="installed_rank">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="648" parent="309" name="version">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="649" parent="309" name="description">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="650" parent="309" name="type">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="651" parent="309" name="script">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="652" parent="309" name="checksum">
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="653" parent="309" name="installed_by">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="654" parent="309" name="installed_on">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="655" parent="309" name="execution_time">
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="656" parent="309" name="success">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="657" parent="309" name="PRIMARY">
      <ColNames>installed_rank</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="658" parent="309" name="flyway_schema_history_s_idx">
      <ColNames>success</ColNames>
      <Type>btree</Type>
    </index>
    <key id="659" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="660" parent="310" name="table_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="661" parent="310" name="table_name">
      <Comment>表名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="662" parent="310" name="table_comment">
      <Comment>表描述</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="663" parent="310" name="sub_table_name">
      <Comment>关联子表的表名</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="664" parent="310" name="sub_table_fk_name">
      <Comment>子表关联的外键名</Comment>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="665" parent="310" name="class_name">
      <Comment>实体类名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="666" parent="310" name="tpl_category">
      <Comment>使用的模板（crud单表操作 tree树表操作）</Comment>
      <DefaultExpression>&apos;crud&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="667" parent="310" name="tpl_web_type">
      <Comment>前端模板类型（element-ui模版 element-plus模版）</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="668" parent="310" name="package_name">
      <Comment>生成包路径</Comment>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="669" parent="310" name="module_name">
      <Comment>生成模块名</Comment>
      <Position>10</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="670" parent="310" name="business_name">
      <Comment>生成业务名</Comment>
      <Position>11</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="671" parent="310" name="function_name">
      <Comment>生成功能名</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="672" parent="310" name="function_author">
      <Comment>生成功能作者</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="673" parent="310" name="gen_type">
      <Comment>生成代码方式（0zip压缩包 1自定义路径）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="674" parent="310" name="gen_path">
      <Comment>生成路径（不填默认项目路径）</Comment>
      <DefaultExpression>&apos;/&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="675" parent="310" name="options">
      <Comment>其它生成选项</Comment>
      <Position>16</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="676" parent="310" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="677" parent="310" name="create_time">
      <Comment>创建时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="678" parent="310" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="679" parent="310" name="update_time">
      <Comment>更新时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="680" parent="310" name="remark">
      <Comment>备注</Comment>
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="681" parent="310" name="PRIMARY">
      <ColNames>table_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="682" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="683" parent="311" name="column_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="684" parent="311" name="table_id">
      <Comment>归属表编号</Comment>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="685" parent="311" name="column_name">
      <Comment>列名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="686" parent="311" name="column_comment">
      <Comment>列描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="687" parent="311" name="column_type">
      <Comment>列类型</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="688" parent="311" name="java_type">
      <Comment>JAVA类型</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="689" parent="311" name="java_field">
      <Comment>JAVA字段名</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="690" parent="311" name="is_pk">
      <Comment>是否主键（1是）</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="691" parent="311" name="is_increment">
      <Comment>是否自增（1是）</Comment>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="692" parent="311" name="is_required">
      <Comment>是否必填（1是）</Comment>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="693" parent="311" name="is_insert">
      <Comment>是否为插入字段（1是）</Comment>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="694" parent="311" name="is_edit">
      <Comment>是否编辑字段（1是）</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="695" parent="311" name="is_list">
      <Comment>是否列表字段（1是）</Comment>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="696" parent="311" name="is_query">
      <Comment>是否查询字段（1是）</Comment>
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="697" parent="311" name="query_type">
      <Comment>查询方式（等于、不等于、大于、小于、范围）</Comment>
      <DefaultExpression>&apos;EQ&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="698" parent="311" name="html_type">
      <Comment>显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）</Comment>
      <Position>16</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="699" parent="311" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="700" parent="311" name="sort">
      <Comment>排序</Comment>
      <Position>18</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="701" parent="311" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="702" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="703" parent="311" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>21</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="704" parent="311" name="update_time">
      <Comment>更新时间</Comment>
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="705" parent="311" name="PRIMARY">
      <ColNames>column_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="706" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="707" parent="312" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="708" parent="312" name="trigger_name">
      <Comment>qrtz_triggers表trigger_name的外键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="709" parent="312" name="trigger_group">
      <Comment>qrtz_triggers表trigger_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="710" parent="312" name="blob_data">
      <Comment>存放持久化Trigger对象</Comment>
      <Position>4</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <index id="711" parent="312" name="PRIMARY">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="712" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="713" parent="313" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="714" parent="313" name="calendar_name">
      <Comment>日历名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="715" parent="313" name="calendar">
      <Comment>存放持久化calendar对象</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <index id="716" parent="313" name="PRIMARY">
      <ColNames>sched_name
calendar_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="717" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="718" parent="314" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="719" parent="314" name="trigger_name">
      <Comment>qrtz_triggers表trigger_name的外键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="720" parent="314" name="trigger_group">
      <Comment>qrtz_triggers表trigger_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="721" parent="314" name="cron_expression">
      <Comment>cron表达式</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="722" parent="314" name="time_zone_id">
      <Comment>时区</Comment>
      <Position>5</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <foreign-key id="723" parent="314" name="qrtz_cron_triggers_ibfk_1">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <RefColNames>sched_name
trigger_name
trigger_group</RefColNames>
      <RefTableName>qrtz_triggers</RefTableName>
    </foreign-key>
    <index id="724" parent="314" name="PRIMARY">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="725" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="726" parent="315" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="727" parent="315" name="entry_id">
      <Comment>调度器实例id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(95)|0s</StoredType>
    </column>
    <column id="728" parent="315" name="trigger_name">
      <Comment>qrtz_triggers表trigger_name的外键</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="729" parent="315" name="trigger_group">
      <Comment>qrtz_triggers表trigger_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="730" parent="315" name="instance_name">
      <Comment>调度器实例名</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="731" parent="315" name="fired_time">
      <Comment>触发的时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="732" parent="315" name="sched_time">
      <Comment>定时器制定的时间</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="733" parent="315" name="priority">
      <Comment>优先级</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="734" parent="315" name="state">
      <Comment>状态</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="735" parent="315" name="job_name">
      <Comment>任务名称</Comment>
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="736" parent="315" name="job_group">
      <Comment>任务组名</Comment>
      <Position>11</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="737" parent="315" name="is_nonconcurrent">
      <Comment>是否并发</Comment>
      <Position>12</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="738" parent="315" name="requests_recovery">
      <Comment>是否接受恢复执行</Comment>
      <Position>13</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <index id="739" parent="315" name="PRIMARY">
      <ColNames>sched_name
entry_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="740" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="741" parent="316" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="742" parent="316" name="job_name">
      <Comment>任务名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="743" parent="316" name="job_group">
      <Comment>任务组名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="744" parent="316" name="description">
      <Comment>相关介绍</Comment>
      <Position>4</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="745" parent="316" name="job_class_name">
      <Comment>执行任务类名称</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="746" parent="316" name="is_durable">
      <Comment>是否持久化</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="747" parent="316" name="is_nonconcurrent">
      <Comment>是否并发</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="748" parent="316" name="is_update_data">
      <Comment>是否更新数据</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="749" parent="316" name="requests_recovery">
      <Comment>是否接受恢复执行</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="750" parent="316" name="job_data">
      <Comment>存放持久化job对象</Comment>
      <Position>10</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <index id="751" parent="316" name="PRIMARY">
      <ColNames>sched_name
job_name
job_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="752" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="753" parent="317" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="754" parent="317" name="lock_name">
      <Comment>悲观锁名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <index id="755" parent="317" name="PRIMARY">
      <ColNames>sched_name
lock_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="756" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="757" parent="318" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="758" parent="318" name="trigger_group">
      <Comment>qrtz_triggers表trigger_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <index id="759" parent="318" name="PRIMARY">
      <ColNames>sched_name
trigger_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="760" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="761" parent="319" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="762" parent="319" name="instance_name">
      <Comment>实例名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="763" parent="319" name="last_checkin_time">
      <Comment>上次检查时间</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="764" parent="319" name="checkin_interval">
      <Comment>检查间隔时间</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <index id="765" parent="319" name="PRIMARY">
      <ColNames>sched_name
instance_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="766" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="767" parent="320" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="768" parent="320" name="trigger_name">
      <Comment>qrtz_triggers表trigger_name的外键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="769" parent="320" name="trigger_group">
      <Comment>qrtz_triggers表trigger_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="770" parent="320" name="repeat_count">
      <Comment>重复的次数统计</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint(7)|0s</StoredType>
    </column>
    <column id="771" parent="320" name="repeat_interval">
      <Comment>重复的间隔时间</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint(12)|0s</StoredType>
    </column>
    <column id="772" parent="320" name="times_triggered">
      <Comment>已经触发的次数</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(10)|0s</StoredType>
    </column>
    <foreign-key id="773" parent="320" name="qrtz_simple_triggers_ibfk_1">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <RefColNames>sched_name
trigger_name
trigger_group</RefColNames>
      <RefTableName>qrtz_triggers</RefTableName>
    </foreign-key>
    <index id="774" parent="320" name="PRIMARY">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="775" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="776" parent="321" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="777" parent="321" name="trigger_name">
      <Comment>qrtz_triggers表trigger_name的外键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="778" parent="321" name="trigger_group">
      <Comment>qrtz_triggers表trigger_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="779" parent="321" name="str_prop_1">
      <Comment>String类型的trigger的第一个参数</Comment>
      <Position>4</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="780" parent="321" name="str_prop_2">
      <Comment>String类型的trigger的第二个参数</Comment>
      <Position>5</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="781" parent="321" name="str_prop_3">
      <Comment>String类型的trigger的第三个参数</Comment>
      <Position>6</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="782" parent="321" name="int_prop_1">
      <Comment>int类型的trigger的第一个参数</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="783" parent="321" name="int_prop_2">
      <Comment>int类型的trigger的第二个参数</Comment>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="784" parent="321" name="long_prop_1">
      <Comment>long类型的trigger的第一个参数</Comment>
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="785" parent="321" name="long_prop_2">
      <Comment>long类型的trigger的第二个参数</Comment>
      <Position>10</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="786" parent="321" name="dec_prop_1">
      <Comment>decimal类型的trigger的第一个参数</Comment>
      <Position>11</Position>
      <StoredType>decimal(13,4 digit)|0s</StoredType>
    </column>
    <column id="787" parent="321" name="dec_prop_2">
      <Comment>decimal类型的trigger的第二个参数</Comment>
      <Position>12</Position>
      <StoredType>decimal(13,4 digit)|0s</StoredType>
    </column>
    <column id="788" parent="321" name="bool_prop_1">
      <Comment>Boolean类型的trigger的第一个参数</Comment>
      <Position>13</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="789" parent="321" name="bool_prop_2">
      <Comment>Boolean类型的trigger的第二个参数</Comment>
      <Position>14</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <foreign-key id="790" parent="321" name="qrtz_simprop_triggers_ibfk_1">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <RefColNames>sched_name
trigger_name
trigger_group</RefColNames>
      <RefTableName>qrtz_triggers</RefTableName>
    </foreign-key>
    <index id="791" parent="321" name="PRIMARY">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="792" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="793" parent="322" name="sched_name">
      <Comment>调度名称</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="794" parent="322" name="trigger_name">
      <Comment>触发器的名字</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="795" parent="322" name="trigger_group">
      <Comment>触发器所属组的名字</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="796" parent="322" name="job_name">
      <Comment>qrtz_job_details表job_name的外键</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="797" parent="322" name="job_group">
      <Comment>qrtz_job_details表job_group的外键</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="798" parent="322" name="description">
      <Comment>相关介绍</Comment>
      <Position>6</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="799" parent="322" name="next_fire_time">
      <Comment>上一次触发时间（毫秒）</Comment>
      <Position>7</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="800" parent="322" name="prev_fire_time">
      <Comment>下一次触发时间（默认为-1表示不触发）</Comment>
      <Position>8</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="801" parent="322" name="priority">
      <Comment>优先级</Comment>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="802" parent="322" name="trigger_state">
      <Comment>触发器状态</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="803" parent="322" name="trigger_type">
      <Comment>触发器的类型</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="804" parent="322" name="start_time">
      <Comment>开始时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="805" parent="322" name="end_time">
      <Comment>结束时间</Comment>
      <Position>13</Position>
      <StoredType>bigint(13)|0s</StoredType>
    </column>
    <column id="806" parent="322" name="calendar_name">
      <Comment>日程表名称</Comment>
      <Position>14</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="807" parent="322" name="misfire_instr">
      <Comment>补偿执行的策略</Comment>
      <Position>15</Position>
      <StoredType>smallint(2)|0s</StoredType>
    </column>
    <column id="808" parent="322" name="job_data">
      <Comment>存放持久化job对象</Comment>
      <Position>16</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <foreign-key id="809" parent="322" name="qrtz_triggers_ibfk_1">
      <ColNames>sched_name
job_name
job_group</ColNames>
      <RefColNames>sched_name
job_name
job_group</RefColNames>
      <RefTableName>qrtz_job_details</RefTableName>
    </foreign-key>
    <index id="810" parent="322" name="PRIMARY">
      <ColNames>sched_name
trigger_name
trigger_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="811" parent="322" name="sched_name_2">
      <ColNames>sched_name
trigger_name</ColNames>
      <Type>btree</Type>
    </index>
    <index id="812" parent="322" name="sched_name">
      <ColNames>sched_name
job_name
job_group</ColNames>
      <Type>btree</Type>
    </index>
    <key id="813" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="814" parent="323" name="config_id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>参数主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(5)|0s</StoredType>
    </column>
    <column id="815" parent="323" name="config_name">
      <Comment>参数名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="816" parent="323" name="config_key">
      <Comment>参数键名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="817" parent="323" name="config_value">
      <Comment>参数键值</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="818" parent="323" name="config_type">
      <Comment>系统内置（Y是 N否）</Comment>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="819" parent="323" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="820" parent="323" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="821" parent="323" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="822" parent="323" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="823" parent="323" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="824" parent="323" name="PRIMARY">
      <ColNames>config_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="825" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="826" parent="324" name="dept_id">
      <AutoIncrement>110</AutoIncrement>
      <Comment>部门id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="827" parent="324" name="parent_id">
      <Comment>父部门id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="828" parent="324" name="ancestors">
      <Comment>祖级列表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="829" parent="324" name="dept_name">
      <Comment>部门名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="830" parent="324" name="order_num">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <column id="831" parent="324" name="leader">
      <Comment>负责人</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="832" parent="324" name="phone">
      <Comment>联系电话</Comment>
      <Position>7</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="833" parent="324" name="email">
      <Comment>邮箱</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="834" parent="324" name="status">
      <Comment>部门状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="835" parent="324" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="836" parent="324" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="837" parent="324" name="create_time">
      <Comment>创建时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="838" parent="324" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="839" parent="324" name="update_time">
      <Comment>更新时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="840" parent="324" name="PRIMARY">
      <ColNames>dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="841" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="842" parent="325" name="dict_code">
      <AutoIncrement>43</AutoIncrement>
      <Comment>字典编码</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="843" parent="325" name="dict_sort">
      <Comment>字典排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <column id="844" parent="325" name="dict_label">
      <Comment>字典标签</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="845" parent="325" name="dict_value">
      <Comment>字典键值</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="846" parent="325" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="847" parent="325" name="css_class">
      <Comment>样式属性（其他样式扩展）</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="848" parent="325" name="list_class">
      <Comment>表格回显样式</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="849" parent="325" name="is_default">
      <Comment>是否默认（Y是 N否）</Comment>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="850" parent="325" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="851" parent="325" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="852" parent="325" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="853" parent="325" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="854" parent="325" name="update_time">
      <Comment>更新时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="855" parent="325" name="remark">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="856" parent="325" name="PRIMARY">
      <ColNames>dict_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="857" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="858" parent="326" name="dict_id">
      <AutoIncrement>13</AutoIncrement>
      <Comment>字典主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="859" parent="326" name="dict_name">
      <Comment>字典名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="860" parent="326" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="861" parent="326" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="862" parent="326" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="863" parent="326" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="864" parent="326" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="865" parent="326" name="update_time">
      <Comment>更新时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="866" parent="326" name="remark">
      <Comment>备注</Comment>
      <Position>9</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="867" parent="326" name="PRIMARY">
      <ColNames>dict_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="868" parent="326" name="dict_type">
      <ColNames>dict_type</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="869" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="870" parent="326" name="dict_type">
      <UnderlyingIndexName>dict_type</UnderlyingIndexName>
    </key>
    <column id="871" parent="327" name="job_id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>任务ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="872" parent="327" name="job_name">
      <Comment>任务名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="873" parent="327" name="job_group">
      <Comment>任务组名</Comment>
      <DefaultExpression>&apos;DEFAULT&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="874" parent="327" name="invoke_target">
      <Comment>调用目标字符串</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="875" parent="327" name="cron_expression">
      <Comment>cron执行表达式</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="876" parent="327" name="misfire_policy">
      <Comment>计划执行错误策略（1立即执行 2执行一次 3放弃执行）</Comment>
      <DefaultExpression>&apos;3&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="877" parent="327" name="concurrent">
      <Comment>是否并发执行（0允许 1禁止）</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="878" parent="327" name="status">
      <Comment>状态（0正常 1暂停）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="879" parent="327" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="880" parent="327" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="881" parent="327" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="882" parent="327" name="update_time">
      <Comment>更新时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="883" parent="327" name="remark">
      <Comment>备注信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="884" parent="327" name="PRIMARY">
      <ColNames>job_id
job_name
job_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="885" parent="327" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="886" parent="328" name="job_log_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>任务日志ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="887" parent="328" name="job_name">
      <Comment>任务名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="888" parent="328" name="job_group">
      <Comment>任务组名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="889" parent="328" name="invoke_target">
      <Comment>调用目标字符串</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="890" parent="328" name="job_message">
      <Comment>日志信息</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="891" parent="328" name="status">
      <Comment>执行状态（0正常 1失败）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="892" parent="328" name="exception_info">
      <Comment>异常信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="893" parent="328" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="894" parent="328" name="PRIMARY">
      <ColNames>job_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="895" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="896" parent="329" name="info_id">
      <AutoIncrement>434</AutoIncrement>
      <Comment>访问ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="897" parent="329" name="user_name">
      <Comment>用户账号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="898" parent="329" name="ipaddr">
      <Comment>登录IP地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="899" parent="329" name="login_location">
      <Comment>登录地点</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="900" parent="329" name="browser">
      <Comment>浏览器类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="901" parent="329" name="os">
      <Comment>操作系统</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="902" parent="329" name="status">
      <Comment>登录状态（0成功 1失败）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="903" parent="329" name="msg">
      <Comment>提示消息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="904" parent="329" name="login_time">
      <Comment>访问时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="905" parent="329" name="PRIMARY">
      <ColNames>info_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="906" parent="329" name="idx_sys_logininfor_s">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="907" parent="329" name="idx_sys_logininfor_lt">
      <ColNames>login_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="908" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="909" parent="330" name="menu_id">
      <AutoIncrement>2008</AutoIncrement>
      <Comment>菜单ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="910" parent="330" name="menu_name">
      <Comment>菜单名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="911" parent="330" name="parent_id">
      <Comment>父菜单ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="912" parent="330" name="order_num">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <column id="913" parent="330" name="path">
      <Comment>路由地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="914" parent="330" name="component">
      <Comment>组件路径</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="915" parent="330" name="query">
      <Comment>路由参数</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="916" parent="330" name="route_name">
      <Comment>路由名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="917" parent="330" name="is_frame">
      <Comment>是否为外链（0是 1否）</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>9</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="918" parent="330" name="is_cache">
      <Comment>是否缓存（0缓存 1不缓存）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="919" parent="330" name="menu_type">
      <Comment>菜单类型（M目录 C菜单 F按钮）</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="920" parent="330" name="visible">
      <Comment>菜单状态（0显示 1隐藏）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="921" parent="330" name="status">
      <Comment>菜单状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="922" parent="330" name="perms">
      <Comment>权限标识</Comment>
      <Position>14</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="923" parent="330" name="icon">
      <Comment>菜单图标</Comment>
      <DefaultExpression>&apos;#&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="924" parent="330" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="925" parent="330" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="926" parent="330" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="927" parent="330" name="update_time">
      <Comment>更新时间</Comment>
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="928" parent="330" name="remark">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="929" parent="330" name="PRIMARY">
      <ColNames>menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="930" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="931" parent="331" name="notice_id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>公告ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <column id="932" parent="331" name="notice_title">
      <Comment>公告标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="933" parent="331" name="notice_type">
      <Comment>公告类型（1通知 2公告）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="934" parent="331" name="notice_content">
      <Comment>公告内容</Comment>
      <Position>4</Position>
      <StoredType>longblob|0s</StoredType>
    </column>
    <column id="935" parent="331" name="status">
      <Comment>公告状态（0正常 1关闭）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="936" parent="331" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="937" parent="331" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="938" parent="331" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="939" parent="331" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="940" parent="331" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="941" parent="331" name="PRIMARY">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="942" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="943" parent="332" name="oper_id">
      <AutoIncrement>190</AutoIncrement>
      <Comment>日志主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="944" parent="332" name="title">
      <Comment>模块标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="945" parent="332" name="business_type">
      <Comment>业务类型（0其它 1新增 2修改 3删除）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="946" parent="332" name="method">
      <Comment>方法名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="947" parent="332" name="request_method">
      <Comment>请求方式</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="948" parent="332" name="operator_type">
      <Comment>操作类别（0其它 1后台用户 2手机端用户）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="949" parent="332" name="oper_name">
      <Comment>操作人员</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="950" parent="332" name="dept_name">
      <Comment>部门名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="951" parent="332" name="oper_url">
      <Comment>请求URL</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="952" parent="332" name="oper_ip">
      <Comment>主机地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="953" parent="332" name="oper_location">
      <Comment>操作地点</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="954" parent="332" name="oper_param">
      <Comment>请求参数</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="955" parent="332" name="json_result">
      <Comment>返回参数</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="956" parent="332" name="status">
      <Comment>操作状态（0正常 1异常）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="957" parent="332" name="error_msg">
      <Comment>错误消息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="958" parent="332" name="oper_time">
      <Comment>操作时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="959" parent="332" name="cost_time">
      <Comment>消耗时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <index id="960" parent="332" name="PRIMARY">
      <ColNames>oper_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="961" parent="332" name="idx_sys_oper_log_bt">
      <ColNames>business_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="962" parent="332" name="idx_sys_oper_log_s">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="963" parent="332" name="idx_sys_oper_log_ot">
      <ColNames>oper_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="964" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="965" parent="333" name="post_id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>岗位ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="966" parent="333" name="post_code">
      <Comment>岗位编码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="967" parent="333" name="post_name">
      <Comment>岗位名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="968" parent="333" name="post_sort">
      <Comment>显示顺序</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <column id="969" parent="333" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="970" parent="333" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="971" parent="333" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="972" parent="333" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="973" parent="333" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="974" parent="333" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="975" parent="333" name="PRIMARY">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="976" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="977" parent="334" name="role_id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="978" parent="334" name="role_name">
      <Comment>角色名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="979" parent="334" name="role_key">
      <Comment>角色权限字符串</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="980" parent="334" name="role_sort">
      <Comment>显示顺序</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <column id="981" parent="334" name="data_scope">
      <Comment>数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="982" parent="334" name="menu_check_strictly">
      <Comment>菜单树选择项是否关联显示</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="983" parent="334" name="dept_check_strictly">
      <Comment>部门树选择项是否关联显示</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="984" parent="334" name="status">
      <Comment>角色状态（0正常 1停用）</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="985" parent="334" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="986" parent="334" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="987" parent="334" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="988" parent="334" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="989" parent="334" name="update_time">
      <Comment>更新时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="990" parent="334" name="remark">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="991" parent="334" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="992" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="993" parent="335" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="994" parent="335" name="dept_id">
      <Comment>部门ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <index id="995" parent="335" name="PRIMARY">
      <ColNames>role_id
dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="996" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="997" parent="336" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="998" parent="336" name="menu_id">
      <Comment>菜单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <index id="999" parent="336" name="PRIMARY">
      <ColNames>role_id
menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1000" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1001" parent="337" name="user_id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1002" parent="337" name="dept_id">
      <Comment>部门ID</Comment>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1003" parent="337" name="user_name">
      <Comment>用户账号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1004" parent="337" name="nick_name">
      <Comment>用户昵称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1005" parent="337" name="user_type">
      <Comment>用户类型（00系统用户）</Comment>
      <DefaultExpression>&apos;00&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="1006" parent="337" name="email">
      <Comment>用户邮箱</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1007" parent="337" name="phonenumber">
      <Comment>手机号码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="1008" parent="337" name="sex">
      <Comment>用户性别（0男 1女 2未知）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1009" parent="337" name="avatar">
      <Comment>头像地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1010" parent="337" name="password">
      <Comment>密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1011" parent="337" name="status">
      <Comment>账号状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1012" parent="337" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1013" parent="337" name="login_ip">
      <Comment>最后登录IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1014" parent="337" name="login_date">
      <Comment>最后登录时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1015" parent="337" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1016" parent="337" name="create_time">
      <Comment>创建时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1017" parent="337" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1018" parent="337" name="update_time">
      <Comment>更新时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1019" parent="337" name="remark">
      <Comment>备注</Comment>
      <Position>19</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="1020" parent="337" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1021" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1022" parent="338" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1023" parent="338" name="post_id">
      <Comment>岗位ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <index id="1024" parent="338" name="PRIMARY">
      <ColNames>user_id
post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1025" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1026" parent="339" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1027" parent="339" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <index id="1028" parent="339" name="PRIMARY">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1029" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>
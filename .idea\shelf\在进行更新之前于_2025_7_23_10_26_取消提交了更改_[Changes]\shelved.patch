Index: db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/ContextManagerImpl.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.db.server.dom.core.engine.impl;\r\n\r\nimport com.db.server.dom.core.engine.ContextManager;\r\nimport com.db.server.dom.core.engine.model.Context;\r\nimport com.fasterxml.jackson.core.type.TypeReference;\r\nimport com.fasterxml.jackson.databind.ObjectMapper;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.stereotype.Component;\r\nimport org.springframework.util.StringUtils;\r\n\r\nimport java.util.HashMap;\r\nimport java.util.Map;\r\nimport java.util.concurrent.ConcurrentHashMap;\r\n\r\n/**\r\n * 上下文管理器实现类\r\n * \r\n * <AUTHOR> * @date 2024-01-01\r\n */\r\n@Slf4j\r\n@Component\r\npublic class ContextManagerImpl implements ContextManager {\r\n\r\n    /**\r\n     * JSON对象映射器\r\n     */\r\n    private final ObjectMapper objectMapper = new ObjectMapper();\r\n\r\n    /**\r\n     * 全局上下文存储\r\n     */\r\n    private final Map<String, Object> globalContextStore = new ConcurrentHashMap<>();\r\n\r\n    @Override\r\n    public Context createContext(String jsonData) {\r\n        try {\r\n            if (!StringUtils.hasText(jsonData)) {\r\n                return new Context();\r\n            }\r\n\r\n            // 解析JSON数据\r\n            Map<String, Object> dataMap = objectMapper.readValue(jsonData, new TypeReference<Map<String, Object>>() {});\r\n            \r\n            Context context = new Context(dataMap);\r\n            context.setOriginalJson(jsonData);\r\n            \r\n            // 设置全局上下文\r\n            context.setGlobalContext(new HashMap<>(globalContextStore));\r\n            \r\n            log.debug(\"创建上下文成功，数据键数量: {}\", dataMap.size());\r\n            return context;\r\n        } catch (Exception e) {\r\n            log.error(\"创建上下文失败\", e);\r\n            return new Context();\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public Object extractValue(Context context, String fieldPath) {\r\n        try {\r\n            if (context == null || !StringUtils.hasText(fieldPath)) {\r\n                return null;\r\n            }\r\n\r\n            // 处理简单字段路径\r\n            if (!fieldPath.contains(\".\")) {\r\n                return context.getValue(fieldPath);\r\n            }\r\n\r\n            // 处理复杂字段路径（如：user.profile.age）\r\n            String[] pathParts = fieldPath.split(\"\\\\.\");\r\n            Object currentValue = context.getDataMap();\r\n\r\n            for (String part : pathParts) {\r\n                if (currentValue == null) {\r\n                    return null;\r\n                }\r\n\r\n                if (currentValue instanceof Map) {\r\n                    @SuppressWarnings(\"unchecked\")\r\n                    Map<String, Object> map = (Map<String, Object>) currentValue;\r\n                    currentValue = map.get(part);\r\n                } else {\r\n                    // 尝试通过反射获取字段值\r\n                    currentValue = getFieldValueByReflection(currentValue, part);\r\n                }\r\n            }\r\n\r\n            return currentValue;\r\n        } catch (Exception e) {\r\n            log.error(\"提取字段值失败 - 字段路径: {}\", fieldPath, e);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public boolean validateContext(Context context) {\r\n        try {\r\n            if (context == null) {\r\n                return false;\r\n            }\r\n\r\n            // 检查数据映射是否为空\r\n            if (context.getDataMap() == null || context.getDataMap().isEmpty()) {\r\n                log.warn(\"上下文数据为空\");\r\n                return false;\r\n            }\r\n\r\n            // 检查原始JSON是否有效\r\n            if (StringUtils.hasText(context.getOriginalJson())) {\r\n                try {\r\n                    objectMapper.readTree(context.getOriginalJson());\r\n                } catch (Exception e) {\r\n                    log.warn(\"原始JSON数据无效\", e);\r\n                    return false;\r\n                }\r\n            }\r\n\r\n            return true;\r\n        } catch (Exception e) {\r\n            log.error(\"验证上下文失败\", e);\r\n            return false;\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public void setGlobalContext(String key, Object value) {\r\n        if (StringUtils.hasText(key)) {\r\n            globalContextStore.put(key, value);\r\n            log.debug(\"设置全局上下文 - 键: {}, 值: {}\", key, value);\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public Object getGlobalContext(String key) {\r\n        if (StringUtils.hasText(key)) {\r\n            return globalContextStore.get(key);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    @Override\r\n    public void clearGlobalContext() {\r\n        globalContextStore.clear();\r\n        log.info(\"清除全局上下文\");\r\n    }\r\n\r\n    /**\r\n     * 通过反射获取字段值\r\n     * \r\n     * @param object 对象\r\n     * @param fieldName 字段名\r\n     * @return 字段值\r\n     */\r\n    private Object getFieldValueByReflection(Object object, String fieldName) {\r\n        try {\r\n            if (object == null || !StringUtils.hasText(fieldName)) {\r\n                return null;\r\n            }\r\n\r\n            Class<?> clazz = object.getClass();\r\n            \r\n            // 尝试获取getter方法\r\n            String getterName = \"get\" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);\r\n            try {\r\n                return clazz.getMethod(getterName).invoke(object);\r\n            } catch (Exception e) {\r\n                // 忽略getter方法不存在的异常\r\n            }\r\n\r\n            // 尝试获取is方法（用于boolean类型）\r\n            String isMethodName = \"is\" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);\r\n            try {\r\n                return clazz.getMethod(isMethodName).invoke(object);\r\n            } catch (Exception e) {\r\n                // 忽略is方法不存在的异常\r\n            }\r\n\r\n            // 尝试直接访问字段\r\n            try {\r\n                java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);\r\n                field.setAccessible(true);\r\n                return field.get(object);\r\n            } catch (Exception e) {\r\n                // 忽略字段不存在的异常\r\n            }\r\n\r\n            return null;\r\n        } catch (Exception e) {\r\n            log.error(\"通过反射获取字段值失败 - 字段名: {}\", fieldName, e);\r\n            return null;\r\n        }\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/ContextManagerImpl.java b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/ContextManagerImpl.java
--- a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/ContextManagerImpl.java	(revision 45efae7a4348c207c1dd2d603e39543f17aabcbf)
+++ b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/ContextManagerImpl.java	(date 1753235254001)
@@ -33,26 +33,61 @@
     private final Map<String, Object> globalContextStore = new ConcurrentHashMap<>();
 
     @Override
-    public Context createContext(String jsonData) {
+    public Context createContext(String inputData) {
         try {
-            if (!StringUtils.hasText(jsonData)) {
+            if (!StringUtils.hasText(inputData)) {
                 return new Context();
             }
 
-            // 解析JSON数据
-            Map<String, Object> dataMap = objectMapper.readValue(jsonData, new TypeReference<Map<String, Object>>() {});
-            
+            Map<String, Object> dataMap = new HashMap<>();
+
+            // 尝试解析为JSON数据
+            if (isJsonFormat(inputData)) {
+                try {
+                    dataMap = objectMapper.readValue(inputData, new TypeReference<Map<String, Object>>() {});
+                    log.debug("解析为JSON格式，数据键数量: {}", dataMap.size());
+                } catch (Exception e) {
+                    log.warn("JSON解析失败，将作为纯文本处理", e);
+                    dataMap.put("rawData", inputData);
+                    dataMap.put("dataType", "text");
+                }
+            } else {
+                // 非JSON格式，作为纯文本处理
+                dataMap.put("rawData", inputData);
+
+                // 检测数据类型
+                String dataType = detectDataType(inputData);
+                dataMap.put("dataType", dataType);
+
+                // 根据数据类型进行特殊处理
+                if ("sql".equals(dataType)) {
+                    dataMap.put("sqlStatement", inputData);
+                    // 可以进一步解析SQL语句的组成部分
+                    parseSqlStatement(inputData, dataMap);
+                }
+
+                log.debug("解析为{}格式", dataType);
+            }
+
             Context context = new Context(dataMap);
-            context.setOriginalJson(jsonData);
-            
+            context.setOriginalJson(inputData);
+
             // 设置全局上下文
             context.setGlobalContext(new HashMap<>(globalContextStore));
-            
-            log.debug("创建上下文成功，数据键数量: {}", dataMap.size());
+
+            log.debug("创建上下文成功，数据类型: {}", dataMap.get("dataType"));
             return context;
         } catch (Exception e) {
             log.error("创建上下文失败", e);
-            return new Context();
+            // 即使失败也要创建一个基本的上下文
+            Context fallbackContext = new Context();
+            Map<String, Object> fallbackData = new HashMap<>();
+            fallbackData.put("rawData", inputData);
+            fallbackData.put("dataType", "unknown");
+            fallbackContext.setDataMap(fallbackData);
+            fallbackContext.setOriginalJson(inputData);
+            fallbackContext.setGlobalContext(new HashMap<>(globalContextStore));
+            return fallbackContext;
         }
     }
 
@@ -107,13 +142,21 @@
                 return false;
             }
 
-            // 检查原始JSON是否有效
+            // 对于非JSON数据，只要有rawData就认为是有效的
+            String dataType = (String) context.getDataMap().get("dataType");
+            if (dataType != null && !"json".equals(dataType)) {
+                Object rawData = context.getDataMap().get("rawData");
+                return rawData != null && StringUtils.hasText(rawData.toString());
+            }
+
+            // 对于JSON数据，检查原始JSON是否有效
             if (StringUtils.hasText(context.getOriginalJson())) {
                 try {
                     objectMapper.readTree(context.getOriginalJson());
                 } catch (Exception e) {
-                    log.warn("原始JSON数据无效", e);
-                    return false;
+                    log.warn("原始JSON数据无效，但可能是其他格式的数据", e);
+                    // 不直接返回false，因为可能是SQL或其他格式
+                    return context.getDataMap().containsKey("rawData");
                 }
             }
 
@@ -192,4 +235,218 @@
             return null;
         }
     }
+
+    /**
+     * 检查字符串是否为JSON格式
+     *
+     * @param input 输入字符串
+     * @return 是否为JSON格式
+     */
+    private boolean isJsonFormat(String input) {
+        if (!StringUtils.hasText(input)) {
+            return false;
+        }
+
+        String trimmed = input.trim();
+        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
+               (trimmed.startsWith("[") && trimmed.endsWith("]"));
+    }
+
+    /**
+     * 检测数据类型
+     *
+     * @param input 输入字符串
+     * @return 数据类型
+     */
+    private String detectDataType(String input) {
+        if (!StringUtils.hasText(input)) {
+            return "empty";
+        }
+
+        String trimmed = input.trim().toUpperCase();
+
+        // 检测SQL语句
+        if (isSqlStatement(trimmed)) {
+            return "sql";
+        }
+
+        // 检测XML
+        if (trimmed.startsWith("<") && trimmed.endsWith(">")) {
+            return "xml";
+        }
+
+        // 检测数字
+        if (trimmed.matches("^-?\\d+(\\.\\d+)?$")) {
+            return "number";
+        }
+
+        // 检测布尔值
+        if ("TRUE".equals(trimmed) || "FALSE".equals(trimmed)) {
+            return "boolean";
+        }
+
+        // 检测URL
+        if (trimmed.startsWith("HTTP://") || trimmed.startsWith("HTTPS://")) {
+            return "url";
+        }
+
+        // 默认为文本
+        return "text";
+    }
+
+    /**
+     * 检查是否为SQL语句
+     *
+     * @param input 输入字符串（已转为大写）
+     * @return 是否为SQL语句
+     */
+    private boolean isSqlStatement(String input) {
+        String[] sqlKeywords = {
+            "SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "ALTER", "DROP",
+            "TRUNCATE", "GRANT", "REVOKE", "COMMIT", "ROLLBACK", "START TRANSACTION", "BEGIN"
+        };
+
+        for (String keyword : sqlKeywords) {
+            if (input.startsWith(keyword)) {
+                return true;
+            }
+        }
+
+        return false;
+    }
+
+    /**
+     * 解析SQL语句
+     *
+     * @param sqlStatement SQL语句
+     * @param dataMap 数据映射
+     */
+    private void parseSqlStatement(String sqlStatement, Map<String, Object> dataMap) {
+        try {
+            String trimmed = sqlStatement.trim().toUpperCase();
+
+            // 提取SQL类型
+            String sqlType = extractSqlType(trimmed);
+            dataMap.put("sqlType", sqlType);
+
+            // 提取表名（简单实现）
+            String tableName = extractTableName(trimmed, sqlType);
+            if (StringUtils.hasText(tableName)) {
+                dataMap.put("tableName", tableName);
+            }
+
+            // 提取数据库名（如果存在）
+            String databaseName = extractDatabaseName(trimmed);
+            if (StringUtils.hasText(databaseName)) {
+                dataMap.put("databaseName", databaseName);
+            }
+
+            log.debug("SQL解析完成 - 类型: {}, 表名: {}", sqlType, tableName);
+        } catch (Exception e) {
+            log.warn("SQL语句解析失败", e);
+        }
+    }
+
+    /**
+     * 提取SQL类型
+     */
+    private String extractSqlType(String sql) {
+        if (sql.startsWith("SELECT")) return "SELECT";
+        if (sql.startsWith("INSERT")) return "INSERT";
+        if (sql.startsWith("UPDATE")) return "UPDATE";
+        if (sql.startsWith("DELETE")) return "DELETE";
+        if (sql.startsWith("CREATE TABLE")) return "CREATE_TABLE";
+        if (sql.startsWith("CREATE DATABASE")) return "CREATE_DATABASE";
+        if (sql.startsWith("ALTER")) return "ALTER";
+        if (sql.startsWith("DROP")) return "DROP";
+        return "UNKNOWN";
+    }
+
+    /**
+     * 提取表名（简单实现）
+     */
+    private String extractTableName(String sql, String sqlType) {
+        try {
+            switch (sqlType) {
+                case "SELECT":
+                    return extractTableFromSelect(sql);
+                case "INSERT":
+                    return extractTableFromInsert(sql);
+                case "UPDATE":
+                    return extractTableFromUpdate(sql);
+                case "DELETE":
+                    return extractTableFromDelete(sql);
+                case "CREATE_TABLE":
+                    return extractTableFromCreateTable(sql);
+                default:
+                    return null;
+            }
+        } catch (Exception e) {
+            log.debug("提取表名失败", e);
+            return null;
+        }
+    }
+
+    private String extractTableFromSelect(String sql) {
+        int fromIndex = sql.indexOf(" FROM ");
+        if (fromIndex > 0) {
+            String afterFrom = sql.substring(fromIndex + 6).trim();
+            String[] parts = afterFrom.split("\\s+");
+            return parts[0].replaceAll("[`\"\\[\\]]", "");
+        }
+        return null;
+    }
+
+    private String extractTableFromInsert(String sql) {
+        int intoIndex = sql.indexOf(" INTO ");
+        if (intoIndex > 0) {
+            String afterInto = sql.substring(intoIndex + 6).trim();
+            String[] parts = afterInto.split("\\s+");
+            return parts[0].replaceAll("[`\"\\[\\]]", "");
+        }
+        return null;
+    }
+
+    private String extractTableFromUpdate(String sql) {
+        int updateIndex = sql.indexOf("UPDATE ");
+        if (updateIndex >= 0) {
+            String afterUpdate = sql.substring(updateIndex + 7).trim();
+            String[] parts = afterUpdate.split("\\s+");
+            return parts[0].replaceAll("[`\"\\[\\]]", "");
+        }
+        return null;
+    }
+
+    private String extractTableFromDelete(String sql) {
+        int fromIndex = sql.indexOf(" FROM ");
+        if (fromIndex > 0) {
+            String afterFrom = sql.substring(fromIndex + 6).trim();
+            String[] parts = afterFrom.split("\\s+");
+            return parts[0].replaceAll("[`\"\\[\\]]", "");
+        }
+        return null;
+    }
+
+    private String extractTableFromCreateTable(String sql) {
+        int tableIndex = sql.indexOf("CREATE TABLE ");
+        if (tableIndex >= 0) {
+            String afterTable = sql.substring(tableIndex + 13).trim();
+            String[] parts = afterTable.split("\\s+");
+            return parts[0].replaceAll("[`\"\\[\\]]", "");
+        }
+        return null;
+    }
+
+    /**
+     * 提取数据库名（简单实现）
+     */
+    private String extractDatabaseName(String sql) {
+        if (sql.contains("CREATE DATABASE ")) {
+            int dbIndex = sql.indexOf("CREATE DATABASE ");
+            String afterDb = sql.substring(dbIndex + 16).trim();
+            String[] parts = afterDb.split("\\s+");
+            return parts[0].replaceAll("[`\"\\[\\]]", "");
+        }
+        return null;
+    }
 }
Index: db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleExecuteRequest.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.db.server.web.api.controller.rule.request;\r\n\r\nimport lombok.Data;\r\n\r\nimport jakarta.validation.constraints.NotBlank;\r\nimport jakarta.validation.constraints.NotNull;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 规则执行请求参数\r\n * \r\n * <AUTHOR> * @date 2024-01-01\r\n */\r\n@Data\r\npublic class RuleExecuteRequest {\r\n\r\n    /**\r\n     * 规则ID\r\n     */\r\n    @NotNull(message = \"规则ID不能为空\")\r\n    private Long ruleId;\r\n\r\n    /**\r\n     * 输入数据（JSON格式）\r\n     */\r\n    @NotBlank(message = \"输入数据不能为空\")\r\n    private String inputData;\r\n\r\n    /**\r\n     * 目标字段路径（可选，如果不指定则使用规则中的配置）\r\n     */\r\n    private String targetField;\r\n\r\n    /**\r\n     * 上下文数据（可选）\r\n     */\r\n    private Map<String, Object> context;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleExecuteRequest.java b/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleExecuteRequest.java
--- a/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleExecuteRequest.java	(revision 45efae7a4348c207c1dd2d603e39543f17aabcbf)
+++ b/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleExecuteRequest.java	(date 1753236040910)
@@ -22,7 +22,7 @@
     private Long ruleId;
 
     /**
-     * 输入数据（JSON格式）
+     * 输入数据（支持JSON、SQL语句、纯文本等格式）
      */
     @NotBlank(message = "输入数据不能为空")
     private String inputData;
Index: db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/RuleExecutionEngineImpl.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.db.server.dom.core.engine.impl;\r\n\r\nimport com.db.server.dom.core.engine.ContextManager;\r\nimport com.db.server.dom.core.engine.RegexInterpreter;\r\nimport com.db.server.dom.core.engine.RuleExecutionEngine;\r\nimport com.db.server.dom.core.engine.model.*;\r\nimport com.db.server.dom.repository.domain.rule.RuleDO;\r\nimport com.db.server.dom.repository.domain.rule.RuleGroupDO;\r\nimport com.db.server.dom.repository.domain.rule.enums.ExecutionStatus;\r\nimport com.db.server.dom.repository.domain.rule.enums.GroupOperator;\r\nimport com.db.server.dom.repository.domain.rule.enums.RuleType;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.stereotype.Component;\r\nimport org.springframework.util.StringUtils;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.List;\r\n\r\n/**\r\n * 规则执行引擎实现类\r\n * \r\n * <AUTHOR> * @date 2024-01-01\r\n */\r\n@Slf4j\r\n@Component\r\npublic class RuleExecutionEngineImpl implements RuleExecutionEngine {\r\n\r\n    @Autowired\r\n    private RegexInterpreter regexInterpreter;\r\n\r\n    @Autowired\r\n    private ContextManager contextManager;\r\n\r\n    @Override\r\n    public ExecutionResult executeRule(RuleDO rule, Context context) {\r\n        long startTime = System.currentTimeMillis();\r\n        \r\n        try {\r\n            // 参数验证\r\n            if (rule == null) {\r\n                return ExecutionResult.systemError(null, null, \"规则对象不能为空\");\r\n            }\r\n            if (context == null) {\r\n                return ExecutionResult.systemError(rule.getId(), rule.getName(), \"执行上下文不能为空\");\r\n            }\r\n            if (!rule.getEnabled()) {\r\n                return ExecutionResult.systemError(rule.getId(), rule.getName(), \"规则已禁用\");\r\n            }\r\n\r\n            // 验证上下文\r\n            if (!contextManager.validateContext(context)) {\r\n                return ExecutionResult.dataError(rule.getId(), rule.getName(), \"执行上下文数据无效\");\r\n            }\r\n\r\n            // 提取目标字段值\r\n            Object targetValue = contextManager.extractValue(context, rule.getTargetField());\r\n            if (targetValue == null) {\r\n                log.debug(\"目标字段值为空 - 规则: {}, 字段: {}\", rule.getName(), rule.getTargetField());\r\n                return createFailureResult(rule, startTime, \"目标字段值为空\", null);\r\n            }\r\n\r\n            // 转换为字符串\r\n            String targetString = convertToString(targetValue);\r\n            if (targetString == null) {\r\n                return ExecutionResult.dataError(rule.getId(), rule.getName(), \"目标字段值无法转换为字符串\");\r\n            }\r\n\r\n            // 执行正则匹配\r\n            MatchResult matchResult = regexInterpreter.match(rule.getPattern(), targetString);\r\n            \r\n            long executionTime = System.currentTimeMillis() - startTime;\r\n            \r\n            if (matchResult.isTimeout()) {\r\n                return ExecutionResult.timeout(rule.getId(), rule.getName());\r\n            }\r\n            \r\n            if (matchResult.getErrorMessage() != null) {\r\n                return ExecutionResult.syntaxError(rule.getId(), rule.getName(), matchResult.getErrorMessage());\r\n            }\r\n\r\n            // 创建执行结果\r\n            ExecutionResult result;\r\n            if (matchResult.isMatched()) {\r\n                result = ExecutionResult.success(rule.getId(), rule.getName(), executionTime);\r\n                result.setMatchedText(matchResult.getMatchedText());\r\n            } else {\r\n                result = ExecutionResult.failure(rule.getId(), rule.getName(), executionTime);\r\n                // 即使匹配失败，也要显示尝试匹配的文本\r\n                result.setMatchedText(\"未匹配到内容，目标文本: \" +\r\n                    (targetString.length() > 100 ? targetString.substring(0, 100) + \"...\" : targetString));\r\n            }\r\n\r\n            // 设置附加信息\r\n            result.setTargetField(rule.getTargetField());\r\n            result.setTargetValue(targetValue);\r\n            result.addMetadata(\"ruleType\", rule.getRuleType());\r\n            result.addMetadata(\"pattern\", rule.getPattern());\r\n            result.addMetadata(\"regexExecutionTime\", matchResult.getExecutionTime());\r\n\r\n            log.debug(\"规则执行完成 - 规则: {}, 结果: {}, 耗时: {}ms\", \r\n                     rule.getName(), result.isResult(), executionTime);\r\n            \r\n            return result;\r\n        } catch (Exception e) {\r\n            long executionTime = System.currentTimeMillis() - startTime;\r\n            log.error(\"规则执行异常 - 规则: {}, 耗时: {}ms\", rule != null ? rule.getName() : \"unknown\", executionTime, e);\r\n            ExecutionResult errorResult = ExecutionResult.systemError(\r\n                rule != null ? rule.getId() : null,\r\n                rule != null ? rule.getName() : null,\r\n                \"规则执行异常: \" + e.getMessage()\r\n            );\r\n            errorResult.setExecutionTime(executionTime);\r\n            return errorResult;\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public ExecutionResult executeRuleGroup(RuleGroupDO ruleGroup, List<RuleDO> rules, Context context) {\r\n        long startTime = System.currentTimeMillis();\r\n        \r\n        try {\r\n            // 参数验证\r\n            if (ruleGroup == null) {\r\n                return ExecutionResult.systemError(null, null, \"规则组对象不能为空\");\r\n            }\r\n            if (rules == null || rules.isEmpty()) {\r\n                return ExecutionResult.systemError(null, ruleGroup.getName(), \"规则组中没有规则\");\r\n            }\r\n            if (context == null) {\r\n                return ExecutionResult.systemError(null, ruleGroup.getName(), \"执行上下文不能为空\");\r\n            }\r\n            if (!ruleGroup.getEnabled()) {\r\n                return ExecutionResult.systemError(null, ruleGroup.getName(), \"规则组已禁用\");\r\n            }\r\n\r\n            // 获取操作符\r\n            GroupOperator operator = GroupOperator.getByCode(ruleGroup.getOperator());\r\n            if (operator == null) {\r\n                return ExecutionResult.systemError(null, ruleGroup.getName(), \"无效的组操作符: \" + ruleGroup.getOperator());\r\n            }\r\n\r\n            // 执行规则组中的所有规则\r\n            List<ExecutionResult> ruleResults = new ArrayList<>();\r\n            boolean groupResult = (operator == GroupOperator.AND); // AND初始为true，OR初始为false\r\n            \r\n            for (RuleDO rule : rules) {\r\n                if (!rule.getEnabled()) {\r\n                    continue; // 跳过禁用的规则\r\n                }\r\n                \r\n                ExecutionResult ruleResult = executeRule(rule, context);\r\n                ruleResults.add(ruleResult);\r\n                \r\n                // 根据操作符计算组结果\r\n                if (operator == GroupOperator.AND) {\r\n                    groupResult = groupResult && ruleResult.isResult();\r\n                    // AND操作：如果有一个失败就可以提前结束\r\n                    if (!ruleResult.isResult()) {\r\n                        log.debug(\"规则组AND操作提前结束 - 规则: {} 失败\", rule.getName());\r\n                        break;\r\n                    }\r\n                } else { // OR操作\r\n                    groupResult = groupResult || ruleResult.isResult();\r\n                    // OR操作：如果有一个成功就可以提前结束\r\n                    if (ruleResult.isResult()) {\r\n                        log.debug(\"规则组OR操作提前结束 - 规则: {} 成功\", rule.getName());\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            long executionTime = System.currentTimeMillis() - startTime;\r\n\r\n            // 创建规则组执行结果\r\n            ExecutionResult result;\r\n            if (groupResult) {\r\n                result = ExecutionResult.success(null, ruleGroup.getName(), executionTime);\r\n            } else {\r\n                result = ExecutionResult.failure(null, ruleGroup.getName(), executionTime);\r\n            }\r\n\r\n            result.setGroupId(ruleGroup.getId());\r\n            result.setGroupName(ruleGroup.getName());\r\n            result.addMetadata(\"operator\", ruleGroup.getOperator());\r\n            result.addMetadata(\"ruleCount\", rules.size());\r\n            result.addMetadata(\"executedRuleCount\", ruleResults.size());\r\n            result.addMetadata(\"ruleResults\", ruleResults);\r\n\r\n            log.info(\"规则组执行完成 - 规则组: {}, 操作符: {}, 结果: {}, 耗时: {}ms\", \r\n                    ruleGroup.getName(), ruleGroup.getOperator(), groupResult, executionTime);\r\n            \r\n            return result;\r\n        } catch (Exception e) {\r\n            long executionTime = System.currentTimeMillis() - startTime;\r\n            log.error(\"规则组执行异常 - 规则组: {}, 耗时: {}ms\", ruleGroup != null ? ruleGroup.getName() : \"unknown\", executionTime, e);\r\n            ExecutionResult errorResult = ExecutionResult.systemError(\r\n                null,\r\n                ruleGroup != null ? ruleGroup.getName() : null,\r\n                \"规则组执行异常: \" + e.getMessage()\r\n            );\r\n            errorResult.setExecutionTime(executionTime);\r\n            return errorResult;\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public List<ExecutionResult> batchExecuteRules(List<RuleDO> rules, Context context) {\r\n        List<ExecutionResult> results = new ArrayList<>();\r\n        \r\n        if (rules == null || rules.isEmpty()) {\r\n            log.warn(\"批量执行规则列表为空\");\r\n            return results;\r\n        }\r\n\r\n        log.info(\"开始批量执行规则 - 规则数量: {}\", rules.size());\r\n        \r\n        for (RuleDO rule : rules) {\r\n            try {\r\n                ExecutionResult result = executeRule(rule, context);\r\n                results.add(result);\r\n            } catch (Exception e) {\r\n                log.error(\"批量执行规则异常 - 规则: {}\", rule.getName(), e);\r\n                ExecutionResult errorResult = ExecutionResult.systemError(\r\n                    rule.getId(), rule.getName(), \"批量执行异常: \" + e.getMessage()\r\n                );\r\n                results.add(errorResult);\r\n            }\r\n        }\r\n\r\n        log.info(\"批量执行规则完成 - 总数: {}, 成功: {}, 失败: {}\", \r\n                results.size(),\r\n                results.stream().mapToInt(r -> r.isResult() ? 1 : 0).sum(),\r\n                results.stream().mapToInt(r -> r.isResult() ? 0 : 1).sum());\r\n        \r\n        return results;\r\n    }\r\n\r\n    @Override\r\n    public ExecutionResult validateRulePattern(String pattern) {\r\n        try {\r\n            if (!StringUtils.hasText(pattern)) {\r\n                return ExecutionResult.syntaxError(null, null, \"正则表达式不能为空\");\r\n            }\r\n\r\n            ValidationResult validationResult = regexInterpreter.validate(pattern);\r\n            \r\n            if (validationResult.isValid()) {\r\n                ExecutionResult result = ExecutionResult.success(null, \"语法验证\", 0);\r\n                result.addMetadata(\"message\", \"正则表达式语法正确\");\r\n                return result;\r\n            } else {\r\n                ExecutionResult result = ExecutionResult.syntaxError(null, \"语法验证\", validationResult.getErrorMessage());\r\n                result.addMetadata(\"errorPosition\", validationResult.getErrorPosition());\r\n                result.addMetadata(\"suggestions\", validationResult.getSuggestions());\r\n                return result;\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"验证正则表达式异常\", e);\r\n            return ExecutionResult.systemError(null, \"语法验证\", \"验证异常: \" + e.getMessage());\r\n        }\r\n    }\r\n\r\n    @Override\r\n    public ExecutionResult testRuleExecution(String pattern, String testData, String targetField) {\r\n        long startTime = System.currentTimeMillis();\r\n        \r\n        try {\r\n            // 参数验证\r\n            if (!StringUtils.hasText(pattern)) {\r\n                return ExecutionResult.syntaxError(null, \"测试执行\", \"正则表达式不能为空\");\r\n            }\r\n            if (!StringUtils.hasText(testData)) {\r\n                return ExecutionResult.dataError(null, \"测试执行\", \"测试数据不能为空\");\r\n            }\r\n\r\n            // 创建测试上下文\r\n            Context testContext = contextManager.createContext(testData);\r\n            if (!contextManager.validateContext(testContext)) {\r\n                return ExecutionResult.dataError(null, \"测试执行\", \"测试数据格式无效\");\r\n            }\r\n\r\n            // 提取目标字段值\r\n            Object targetValue;\r\n            if (StringUtils.hasText(targetField)) {\r\n                targetValue = contextManager.extractValue(testContext, targetField);\r\n            } else {\r\n                // 如果没有指定目标字段，直接使用测试数据\r\n                targetValue = testData;\r\n            }\r\n\r\n            if (targetValue == null) {\r\n                return ExecutionResult.dataError(null, \"测试执行\", \"目标字段值为空\");\r\n            }\r\n\r\n            // 转换为字符串\r\n            String targetString = convertToString(targetValue);\r\n            if (targetString == null) {\r\n                return ExecutionResult.dataError(null, \"测试执行\", \"目标字段值无法转换为字符串\");\r\n            }\r\n\r\n            // 执行正则匹配\r\n            MatchResult matchResult = regexInterpreter.match(pattern, targetString);\r\n            \r\n            long executionTime = System.currentTimeMillis() - startTime;\r\n            \r\n            if (matchResult.isTimeout()) {\r\n                return ExecutionResult.timeout(null, \"测试执行\");\r\n            }\r\n            \r\n            if (matchResult.getErrorMessage() != null) {\r\n                return ExecutionResult.syntaxError(null, \"测试执行\", matchResult.getErrorMessage());\r\n            }\r\n\r\n            // 创建测试结果\r\n            ExecutionResult result;\r\n            if (matchResult.isMatched()) {\r\n                result = ExecutionResult.success(null, \"测试执行\", executionTime);\r\n                result.setMatchedText(matchResult.getMatchedText());\r\n                result.addMetadata(\"matchGroups\", matchResult.getGroups());\r\n                result.addMetadata(\"startPosition\", matchResult.getStartPosition());\r\n                result.addMetadata(\"endPosition\", matchResult.getEndPosition());\r\n            } else {\r\n                result = ExecutionResult.failure(null, \"测试执行\", executionTime);\r\n            }\r\n\r\n            result.setTargetField(targetField);\r\n            result.setTargetValue(targetValue);\r\n            result.addMetadata(\"pattern\", pattern);\r\n            result.addMetadata(\"testData\", testData);\r\n            result.addMetadata(\"regexExecutionTime\", matchResult.getExecutionTime());\r\n\r\n            return result;\r\n        } catch (Exception e) {\r\n            long executionTime = System.currentTimeMillis() - startTime;\r\n            log.error(\"测试规则执行异常，耗时: {}ms\", executionTime, e);\r\n            ExecutionResult errorResult = ExecutionResult.systemError(null, \"测试执行\", \"测试执行异常: \" + e.getMessage());\r\n            errorResult.setExecutionTime(executionTime);\r\n            return errorResult;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 创建失败的执行结果\r\n     * \r\n     * @param rule 规则对象\r\n     * @param startTime 开始时间\r\n     * @param reason 失败原因\r\n     * @param targetValue 目标值\r\n     * @return 执行结果\r\n     */\r\n    private ExecutionResult createFailureResult(RuleDO rule, long startTime, String reason, Object targetValue) {\r\n        long executionTime = System.currentTimeMillis() - startTime;\r\n        ExecutionResult result = ExecutionResult.failure(rule.getId(), rule.getName(), executionTime);\r\n        result.setTargetField(rule.getTargetField());\r\n        result.setTargetValue(targetValue);\r\n        result.addMetadata(\"failureReason\", reason);\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 将对象转换为字符串\r\n     * \r\n     * @param value 对象值\r\n     * @return 字符串值\r\n     */\r\n    private String convertToString(Object value) {\r\n        if (value == null) {\r\n            return null;\r\n        }\r\n        if (value instanceof String) {\r\n            return (String) value;\r\n        }\r\n        return value.toString();\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/RuleExecutionEngineImpl.java b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/RuleExecutionEngineImpl.java
--- a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/RuleExecutionEngineImpl.java	(revision 45efae7a4348c207c1dd2d603e39543f17aabcbf)
+++ b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/impl/RuleExecutionEngineImpl.java	(date 1753235300831)
@@ -285,9 +285,34 @@
             Object targetValue;
             if (StringUtils.hasText(targetField)) {
                 targetValue = contextManager.extractValue(testContext, targetField);
+                // 如果指定了目标字段但提取失败，检查是否为非JSON数据
+                if (targetValue == null) {
+                    String dataType = (String) testContext.getDataMap().get("dataType");
+                    if (!"json".equals(dataType)) {
+                        // 对于非JSON数据，如果目标字段是特殊字段，尝试从解析结果中获取
+                        if ("rawData".equals(targetField) || "sqlStatement".equals(targetField)) {
+                            targetValue = testContext.getDataMap().get(targetField);
+                        } else if ("tableName".equals(targetField) || "databaseName".equals(targetField) || "sqlType".equals(targetField)) {
+                            targetValue = testContext.getDataMap().get(targetField);
+                        }
+                    }
+                }
             } else {
-                // 如果没有指定目标字段，直接使用测试数据
-                targetValue = testData;
+                // 如果没有指定目标字段，根据数据类型决定使用什么值
+                String dataType = (String) testContext.getDataMap().get("dataType");
+                if ("sql".equals(dataType)) {
+                    // 对于SQL语句，默认使用原始SQL语句
+                    targetValue = testContext.getDataMap().get("rawData");
+                } else if ("json".equals(dataType)) {
+                    // 对于JSON数据，使用原始JSON
+                    targetValue = testData;
+                } else {
+                    // 对于其他类型，使用原始数据
+                    targetValue = testContext.getDataMap().get("rawData");
+                    if (targetValue == null) {
+                        targetValue = testData;
+                    }
+                }
             }
 
             if (targetValue == null) {
Index: db-manage-web/docs/多格式数据支持指南.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/docs/多格式数据支持指南.md b/db-manage-web/docs/多格式数据支持指南.md
new file mode 100644
--- /dev/null	(date 1753236324739)
+++ b/db-manage-web/docs/多格式数据支持指南.md	(date 1753236324739)
@@ -0,0 +1,179 @@
+# 规则引擎多格式数据支持指南
+
+## 📋 概述
+
+规则引擎现在支持多种数据格式的输入，不再局限于JSON格式。系统会自动检测数据类型并进行相应的解析和处理。
+
+## 🎯 支持的数据格式
+
+### 1. JSON格式
+```json
+{
+  "name": "张三",
+  "email": "<EMAIL>",
+  "age": 25,
+  "address": "北京市朝阳区"
+}
+```
+
+### 2. SQL语句
+```sql
+-- SELECT语句
+SELECT * FROM users WHERE status = 'ACTIVE';
+
+-- CREATE TABLE语句
+CREATE TABLE users (
+  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
+  username VARCHAR(50) NOT NULL CHARSET utf8mb4 COMMENT '用户名',
+  email VARCHAR(100) NOT NULL CHARSET utf8mb4 COMMENT '邮箱'
+) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
+
+-- UPDATE语句
+UPDATE users SET status = 'INACTIVE' WHERE id = 123;
+
+-- DELETE语句
+DELETE FROM users WHERE status = 'DELETED';
+```
+
+### 3. 数据库对象名称
+```text
+# 表名
+user_profiles
+order_details
+
+# 字段名
+user_id
+create_time
+
+# 索引名
+idx_users_email
+uk_users_username
+pk_users
+fk_users_role_id
+```
+
+### 4. 纯文本
+```text
+任意文本内容
+URL地址: https://example.com
+数字值: 12345
+布尔值: true
+```
+
+## 🔧 智能解析功能
+
+### SQL语句解析
+系统会自动解析SQL语句并提取以下信息：
+- **sqlType**: SQL语句类型（SELECT、INSERT、UPDATE、DELETE、CREATE_TABLE等）
+- **tableName**: 表名（从SQL语句中自动提取）
+- **databaseName**: 数据库名（从CREATE DATABASE语句中提取）
+- **sqlStatement**: 完整的SQL语句
+
+### 数据类型检测
+系统会自动检测以下数据类型：
+- **json**: JSON对象或数组
+- **sql**: SQL语句
+- **xml**: XML文档
+- **number**: 数字值
+- **boolean**: 布尔值
+- **url**: URL地址
+- **text**: 纯文本
+
+## 🎨 目标字段支持
+
+在规则配置中，可以使用以下特殊目标字段：
+
+### 通用字段
+- `rawData`: 原始输入数据
+- `dataType`: 数据类型
+
+### SQL相关字段
+- `sqlStatement`: SQL语句内容
+- `sqlType`: SQL语句类型
+- `tableName`: 表名
+- `databaseName`: 数据库名
+
+### JSON相关字段
+- 任意JSON对象的属性路径，如：`user.name`、`order.items[0].price`
+
+## 📝 使用示例
+
+### 示例1：验证SQL规范
+**规则**: 禁止使用SELECT *
+**目标字段**: `sqlStatement`
+**测试数据**:
+```sql
+SELECT * FROM users WHERE status = 'ACTIVE';
+```
+**结果**: 匹配（违反规范）
+
+### 示例2：验证表名规范
+**规则**: 表名必须以字母开头，只能包含字母、数字和下划线
+**目标字段**: `tableName`
+**测试数据**:
+```sql
+CREATE TABLE user_profiles (id INT PRIMARY KEY);
+```
+**结果**: 匹配（符合规范）
+
+### 示例3：验证字段名规范
+**规则**: 字段名必须使用小写字母和下划线
+**目标字段**: `rawData`
+**测试数据**:
+```text
+user_id
+```
+**结果**: 匹配（符合规范）
+
+### 示例4：验证JSON数据
+**规则**: 邮箱格式验证
+**目标字段**: `email`
+**测试数据**:
+```json
+{
+  "name": "张三",
+  "email": "<EMAIL>"
+}
+```
+**结果**: 匹配（符合规范）
+
+## 🚀 使用场景
+
+### 数据库规范检查
+- SQL语句规范验证
+- 表名、字段名命名规范检查
+- 索引命名规范验证
+- 数据库创建语句检查
+
+### 数据验证
+- JSON数据格式验证
+- 字段值格式检查
+- 业务规则验证
+
+### 代码质量检查
+- SQL代码质量检查
+- 数据库设计规范验证
+- 开发规范检查
+
+## 💡 最佳实践
+
+1. **选择合适的目标字段**: 根据规则类型选择最合适的目标字段
+2. **使用测试数据模板**: 利用系统提供的测试数据模板快速测试
+3. **组合使用规则**: 通过规则组实现复合检查
+4. **定期验证规则**: 使用调试功能验证规则的正确性
+
+## 🔍 调试技巧
+
+1. **使用调试页面**: 在调试页面测试规则和数据的匹配情况
+2. **查看解析结果**: 观察系统如何解析不同格式的数据
+3. **测试边界情况**: 测试各种边界情况和异常数据
+4. **验证目标字段**: 确认目标字段能正确提取到期望的值
+
+## ⚠️ 注意事项
+
+1. **数据格式自动检测**: 系统会自动检测数据格式，无需手动指定
+2. **容错处理**: 即使数据解析失败，系统也会创建基本的上下文
+3. **性能考虑**: 复杂的SQL解析可能会影响性能，建议合理使用
+4. **兼容性**: 保持与原有JSON格式的完全兼容
+
+通过这个多格式数据支持功能，规则引擎现在可以处理更广泛的数据类型，特别适合数据库规范检查和SQL质量验证等场景。
Index: db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleGroupExecuteRequest.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.db.server.web.api.controller.rule.request;\r\n\r\nimport lombok.Data;\r\n\r\nimport jakarta.validation.constraints.NotBlank;\r\nimport jakarta.validation.constraints.NotNull;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 规则组执行请求参数\r\n * \r\n * <AUTHOR> * @date 2024-01-01\r\n */\r\n@Data\r\npublic class RuleGroupExecuteRequest {\r\n\r\n    /**\r\n     * 规则组ID\r\n     */\r\n    @NotNull(message = \"规则组ID不能为空\")\r\n    private Long groupId;\r\n\r\n    /**\r\n     * 输入数据（JSON格式）\r\n     */\r\n    @NotBlank(message = \"输入数据不能为空\")\r\n    private String inputData;\r\n\r\n    /**\r\n     * 上下文数据（可选）\r\n     */\r\n    private Map<String, Object> context;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleGroupExecuteRequest.java b/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleGroupExecuteRequest.java
--- a/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleGroupExecuteRequest.java	(revision 45efae7a4348c207c1dd2d603e39543f17aabcbf)
+++ b/db-manage-web/db-model/db-server-api/src/main/java/com/db/server/web/api/controller/rule/request/RuleGroupExecuteRequest.java	(date 1753236061685)
@@ -22,7 +22,7 @@
     private Long groupId;
 
     /**
-     * 输入数据（JSON格式）
+     * 输入数据（支持JSON、SQL语句、纯文本等格式）
      */
     @NotBlank(message = "输入数据不能为空")
     private String inputData;
Index: db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/ContextManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.db.server.dom.core.engine;\r\n\r\nimport com.db.server.dom.core.engine.model.Context;\r\n\r\n/**\r\n * 上下文管理器接口\r\n * \r\n * <AUTHOR> * @date 2024-01-01\r\n */\r\npublic interface ContextManager {\r\n\r\n    /**\r\n     * 创建上下文\r\n     * \r\n     * @param jsonData JSON格式的数据\r\n     * @return 上下文对象\r\n     */\r\n    Context createContext(String jsonData);\r\n\r\n    /**\r\n     * 从上下文中提取字段值\r\n     * \r\n     * @param context 上下文对象\r\n     * @param fieldPath 字段路径（如：user.profile.age）\r\n     * @return 字段值\r\n     */\r\n    Object extractValue(Context context, String fieldPath);\r\n\r\n    /**\r\n     * 验证上下文数据\r\n     * \r\n     * @param context 上下文对象\r\n     * @return 是否有效\r\n     */\r\n    boolean validateContext(Context context);\r\n\r\n    /**\r\n     * 设置全局上下文数据\r\n     * \r\n     * @param key 键\r\n     * @param value 值\r\n     */\r\n    void setGlobalContext(String key, Object value);\r\n\r\n    /**\r\n     * 获取全局上下文数据\r\n     * \r\n     * @param key 键\r\n     * @return 值\r\n     */\r\n    Object getGlobalContext(String key);\r\n\r\n    /**\r\n     * 清除全局上下文\r\n     */\r\n    void clearGlobalContext();\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/ContextManager.java b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/ContextManager.java
--- a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/ContextManager.java	(revision 45efae7a4348c207c1dd2d603e39543f17aabcbf)
+++ b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/core/engine/ContextManager.java	(date 1753235507689)
@@ -12,11 +12,11 @@
 
     /**
      * 创建上下文
-     * 
-     * @param jsonData JSON格式的数据
+     *
+     * @param inputData 输入数据，支持多种格式：JSON、SQL语句、纯文本等
      * @return 上下文对象
      */
-    Context createContext(String jsonData);
+    Context createContext(String inputData);
 
     /**
      * 从上下文中提取字段值
Index: db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/api/service/rule/RuleEngineFacade.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.db.server.dom.api.service.rule;\r\n\r\nimport com.db.server.dom.core.engine.model.ExecutionResult;\r\nimport com.db.server.tools.base.wrapper.result.DataResult;\r\n\r\nimport jakarta.validation.constraints.NotBlank;\r\nimport jakarta.validation.constraints.NotNull;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.concurrent.CompletableFuture;\r\n\r\n/**\r\n * 规则引擎门面接口\r\n * 提供简化的规则执行调用方式，供其他模块使用\r\n * \r\n * <AUTHOR> * @date 2024-01-01\r\n */\r\npublic interface RuleEngineFacade {\r\n\r\n    /**\r\n     * 根据规则ID执行规则（最常用）\r\n     * \r\n     * @param ruleId 规则ID\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果\r\n     */\r\n    DataResult<ExecutionResult> executeRule(@NotNull Long ruleId, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 根据规则名称执行规则\r\n     * \r\n     * @param ruleName 规则名称\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果\r\n     */\r\n    DataResult<ExecutionResult> executeRuleByName(@NotBlank String ruleName, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 根据规则组ID执行规则组\r\n     * \r\n     * @param groupId 规则组ID\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果\r\n     */\r\n    DataResult<ExecutionResult> executeRuleGroup(@NotNull Long groupId, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 根据规则组名称执行规则组\r\n     * \r\n     * @param groupName 规则组名称\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果\r\n     */\r\n    DataResult<ExecutionResult> executeRuleGroupByName(@NotBlank String groupName, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 批量执行多个规则\r\n     * \r\n     * @param ruleIds 规则ID列表\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果列表\r\n     */\r\n    DataResult<List<ExecutionResult>> batchExecuteRules(@NotNull List<Long> ruleIds, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 根据规则类型执行规则\r\n     * \r\n     * @param ruleType 规则类型\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果列表\r\n     */\r\n    DataResult<List<ExecutionResult>> executeRulesByType(@NotBlank String ruleType, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 执行所有启用的规则\r\n     * \r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 执行结果列表\r\n     */\r\n    DataResult<List<ExecutionResult>> executeAllEnabledRules(@NotBlank String inputData);\r\n\r\n    /**\r\n     * 异步执行规则\r\n     * \r\n     * @param ruleId 规则ID\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 异步执行结果\r\n     */\r\n    CompletableFuture<DataResult<ExecutionResult>> executeRuleAsync(@NotNull Long ruleId, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 异步执行规则组\r\n     * \r\n     * @param groupId 规则组ID\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 异步执行结果\r\n     */\r\n    CompletableFuture<DataResult<ExecutionResult>> executeRuleGroupAsync(@NotNull Long groupId, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 简单的数据验证（返回boolean）\r\n     * \r\n     * @param ruleId 规则ID\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 验证结果（true-通过，false-不通过）\r\n     */\r\n    boolean validateData(@NotNull Long ruleId, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 简单的数据验证（通过规则名称）\r\n     * \r\n     * @param ruleName 规则名称\r\n     * @param inputData 输入数据（JSON字符串）\r\n     * @return 验证结果（true-通过，false-不通过）\r\n     */\r\n    boolean validateDataByName(@NotBlank String ruleName, @NotBlank String inputData);\r\n\r\n    /**\r\n     * 批量数据验证\r\n     * \r\n     * @param ruleId 规则ID\r\n     * @param inputDataList 输入数据列表\r\n     * @return 验证结果映射（输入数据 -> 验证结果）\r\n     */\r\n    DataResult<Map<String, Boolean>> batchValidateData(@NotNull Long ruleId, @NotNull List<String> inputDataList);\r\n\r\n    /**\r\n     * 快速规则测试（用于调试）\r\n     * \r\n     * @param pattern 正则表达式\r\n     * @param testData 测试数据\r\n     * @param targetField 目标字段\r\n     * @return 测试结果\r\n     */\r\n    DataResult<ExecutionResult> quickTest(@NotBlank String pattern, @NotBlank String testData, String targetField);\r\n\r\n    /**\r\n     * 检查规则引擎健康状态\r\n     *\r\n     * @return 健康状态\r\n     */\r\n    DataResult<Boolean> checkHealth();\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/api/service/rule/RuleEngineFacade.java b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/api/service/rule/RuleEngineFacade.java
--- a/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/api/service/rule/RuleEngineFacade.java	(revision 45efae7a4348c207c1dd2d603e39543f17aabcbf)
+++ b/db-manage-web/db-model/db-server-domain/src/main/java/com/db/server/dom/api/service/rule/RuleEngineFacade.java	(date 1753236017326)
@@ -20,27 +20,27 @@
 
     /**
      * 根据规则ID执行规则（最常用）
-     * 
+     *
      * @param ruleId 规则ID
-     * @param inputData 输入数据（JSON字符串）
+     * @param inputData 输入数据（支持JSON、SQL语句、纯文本等格式）
      * @return 执行结果
      */
     DataResult<ExecutionResult> executeRule(@NotNull Long ruleId, @NotBlank String inputData);
 
     /**
      * 根据规则名称执行规则
-     * 
+     *
      * @param ruleName 规则名称
-     * @param inputData 输入数据（JSON字符串）
+     * @param inputData 输入数据（支持JSON、SQL语句、纯文本等格式）
      * @return 执行结果
      */
     DataResult<ExecutionResult> executeRuleByName(@NotBlank String ruleName, @NotBlank String inputData);
 
     /**
      * 根据规则组ID执行规则组
-     * 
+     *
      * @param groupId 规则组ID
-     * @param inputData 输入数据（JSON字符串）
+     * @param inputData 输入数据（支持JSON、SQL语句、纯文本等格式）
      * @return 执行结果
      */
     DataResult<ExecutionResult> executeRuleGroup(@NotNull Long groupId, @NotBlank String inputData);
@@ -74,26 +74,26 @@
 
     /**
      * 执行所有启用的规则
-     * 
-     * @param inputData 输入数据（JSON字符串）
+     *
+     * @param inputData 输入数据（支持JSON、SQL语句、纯文本等格式）
      * @return 执行结果列表
      */
     DataResult<List<ExecutionResult>> executeAllEnabledRules(@NotBlank String inputData);
 
     /**
      * 异步执行规则
-     * 
+     *
      * @param ruleId 规则ID
-     * @param inputData 输入数据（JSON字符串）
+     * @param inputData 输入数据（支持JSON、SQL语句、纯文本等格式）
      * @return 异步执行结果
      */
     CompletableFuture<DataResult<ExecutionResult>> executeRuleAsync(@NotNull Long ruleId, @NotBlank String inputData);
 
     /**
      * 异步执行规则组
-     * 
+     *
      * @param groupId 规则组ID
-     * @param inputData 输入数据（JSON字符串）
+     * @param inputData 输入数据（支持JSON、SQL语句、纯文本等格式）
      * @return 异步执行结果
      */
     CompletableFuture<DataResult<ExecutionResult>> executeRuleGroupAsync(@NotNull Long groupId, @NotBlank String inputData);

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6ebcb9d6-2205-46fe-8da4-61e0c0f6fa93" name="Changes" comment="新增规则引擎">
      <change beforePath="$PROJECT_DIR$/db-manage-web/db-admin/src/main/java/com/db/RuoYiApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/db-manage-web/db-admin/src/main/java/com/db/RuoYiApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/db-manage-web/db-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/db-manage-web/db-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/db-manage-web/db-framework/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/db-manage-web/db-framework/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/db-manage-web/db-framework/src/main/java/com/db/framework/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/db-manage-web/db-framework/src/main/java/com/db/framework/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dbmanage-ui/config/routes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/dbmanage-ui/config/routes.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_COMMON_BRANCH" value="master" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/db-manage-web" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev" />
                    <option name="lastUsedInstant" value="1752112986" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1752112985" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/db-manage-web" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev" />
                    <option name="lastUsedInstant" value="1752112987" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1752112986" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/dbmanage-ui" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\developertool\apache-maven-3.3.9" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\developertool\apache-maven-3.3.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zfCtWdBDVrkSeP1R1NujhabeUe" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="abbreviatePackageNames" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.db [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.db [compile].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/WorkSpaceQH/dbmanage/db-manage-web/docs&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Debug&quot;,
    &quot;npm.serve.executor&quot;: &quot;Run&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;D:\\WorkSpaceQH\\dbmanage\\dbmanage-ui\\node_modules\\prettier&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.zeroturnaround.javarebel.idea.plugin.settings.JRebelSettingsComponent&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\WorkSpaceQH\\dbmanage\\dbmanage-ui\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\WorkSpaceQH\dbmanage\db-manage-web\docs" />
      <recent name="D:\WorkSpaceQH\dbmanage\dbmanage-ui\src\types\database" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\WorkSpaceQH\dbmanage\db-manage-web\docs" />
      <recent name="D:\WorkSpaceQH\dbmanage\dbmanage-ui\src\pages\Database" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.db.server.dom.api.model" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.RuoYiApplication">
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="db-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.db.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/dbmanage-ui/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="serve" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/dbmanage-ui/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.serve" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6ebcb9d6-2205-46fe-8da4-61e0c0f6fa93" name="Changes" comment="" />
      <created>1752112956081</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752112956081</updated>
      <workItem from="1752112957511" duration="594000" />
      <workItem from="1752113579308" duration="18083000" />
      <workItem from="1752195884166" duration="29480000" />
      <workItem from="1752465912941" duration="5250000" />
      <workItem from="1752480660860" duration="7888000" />
      <workItem from="1752488623556" duration="4672000" />
      <workItem from="1752542033419" duration="490000" />
      <workItem from="1752572210706" duration="6139000" />
      <workItem from="1752630957213" duration="3996000" />
      <workItem from="1752634979582" duration="12788000" />
      <workItem from="1752651980765" duration="2337000" />
      <workItem from="1752654335646" duration="42000" />
      <workItem from="1752654395747" duration="1707000" />
      <workItem from="1752656193013" duration="1141000" />
      <workItem from="1752657939969" duration="13896000" />
      <workItem from="1752720555343" duration="1183000" />
      <workItem from="1752721973650" duration="6752000" />
      <workItem from="1752733007430" duration="2258000" />
      <workItem from="1752735290747" duration="713000" />
      <workItem from="1752736038112" duration="20389000" />
      <workItem from="1752819559121" duration="14145000" />
      <workItem from="1753060799678" duration="29178000" />
      <workItem from="1753156404746" duration="1621000" />
      <workItem from="1753158049100" duration="68000" />
      <workItem from="1753158131377" duration="177000" />
      <workItem from="1753158330169" duration="465000" />
      <workItem from="1753158859567" duration="2870000" />
      <workItem from="1753161775949" duration="639000" />
      <workItem from="1753162462857" duration="11776000" />
      <workItem from="1753175096629" duration="8422000" />
      <workItem from="1753233667750" duration="7104000" />
      <workItem from="1753321061446" duration="11302000" />
      <workItem from="1753781365457" duration="2795000" />
    </task>
    <task id="LOCAL-00001" summary="新增集成kafka">
      <option name="closed" value="true" />
      <created>1752475485403</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752475485403</updated>
    </task>
    <task id="LOCAL-00002" summary="新增集成kafka">
      <option name="closed" value="true" />
      <created>1752476034992</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752476034992</updated>
    </task>
    <task id="LOCAL-00003" summary="新增接口不启动监听一次性消费主题里所有消息">
      <option name="closed" value="true" />
      <created>1752477405143</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752477405143</updated>
    </task>
    <task id="LOCAL-00004" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752655420281</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752655420282</updated>
    </task>
    <task id="LOCAL-00005" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752665143360</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752665143360</updated>
    </task>
    <task id="LOCAL-00006" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752721719810</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752721719810</updated>
    </task>
    <task id="LOCAL-00007" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752733359882</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752733359882</updated>
    </task>
    <task id="LOCAL-00008" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752740953934</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752740953934</updated>
    </task>
    <task id="LOCAL-00009" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752741494738</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752741494738</updated>
    </task>
    <task id="LOCAL-00010" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752746500609</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752746500609</updated>
    </task>
    <task id="LOCAL-00011" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752802595377</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752802595377</updated>
    </task>
    <task id="LOCAL-00012" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1752831933522</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752831933522</updated>
    </task>
    <task id="LOCAL-00013" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1753067982911</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753067982911</updated>
    </task>
    <task id="LOCAL-00014" summary="新增订阅">
      <option name="closed" value="true" />
      <created>1753079482070</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753079482070</updated>
    </task>
    <task id="LOCAL-00015" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753153130908</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753153130908</updated>
    </task>
    <task id="LOCAL-00016" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753157953597</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753157953597</updated>
    </task>
    <task id="LOCAL-00017" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753166687031</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753166687032</updated>
    </task>
    <task id="LOCAL-00018" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753169994407</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753169994407</updated>
    </task>
    <task id="LOCAL-00019" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753172064148</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753172064149</updated>
    </task>
    <task id="LOCAL-00020" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753177793367</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753177793367</updated>
    </task>
    <task id="LOCAL-00021" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753234808413</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753234808413</updated>
    </task>
    <task id="LOCAL-00022" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753240471313</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753240471313</updated>
    </task>
    <task id="LOCAL-00023" summary="新增规则引擎">
      <option name="closed" value="true" />
      <created>1753240575053</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753240575053</updated>
    </task>
    <option name="localTasksCounter" value="24" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="新增集成kafka" />
    <MESSAGE value="新增接口不启动监听一次性消费主题里所有消息" />
    <MESSAGE value="新增订阅" />
    <MESSAGE value="新增规则引擎" />
    <option name="LAST_COMMIT_MESSAGE" value="新增规则引擎" />
  </component>
</project>
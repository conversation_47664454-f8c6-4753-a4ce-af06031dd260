<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-251.25410.129">
    <data-source name="5@192.168.1.136" uuid="23eb6992-4e84-45f1-95ed-737fc4949541">
      <database-info product="" version="" jdbc-version="" driver-name="" driver-version="" dbms="REDIS" />
      <schema-mapping />
    </data-source>
    <data-source name="mysql" uuid="d92cc5df-1bbc-4ae0-a6a6-63e47036a820">
      <database-info product="MySQL" version="5.7.40" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.40" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="db-manage1" />
        </introspection-scope>
      </schema-mapping>
      <load-sources>user_and_system_sources</load-sources>
    </data-source>
  </component>
</project>
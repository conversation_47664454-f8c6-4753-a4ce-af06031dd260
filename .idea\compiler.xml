<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Annotation profile for db" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <option name="mapstruct.disableBuilders" value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="db-kingbase" />
        <module name="db-server-api" />
        <module name="db-postgresql" />
        <module name="db-admin" />
        <module name="db-framework" />
        <module name="db-mysql" />
        <module name="db-oracle" />
        <module name="db-server-tools" />
        <module name="db-generator" />
        <module name="db-oceanbase" />
        <module name="db-quartz" />
        <module name="db-server-domain" />
        <module name="db-sqlite" />
        <module name="db-dm" />
        <module name="db-h2" />
        <module name="db-common" />
        <module name="db-core-spi" />
        <module name="db-sqlserver" />
        <module name="db-system" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="db" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-admin" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-common" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-core-spi" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-dm" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-framework" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-generator" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-h2" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-kingbase" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-model" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-mysql" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-oceanbase" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-oracle" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-plugins" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-postgresql" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-quartz" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-server-api" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-server-domain" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-server-tools" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-sqlite" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-sqlserver" options="-parameters -Amapstruct.disableBuilders=true" />
      <module name="db-system" options="-parameters -Amapstruct.disableBuilders=true" />
    </option>
  </component>
</project>
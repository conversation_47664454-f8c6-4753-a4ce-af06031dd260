package com.db.config;

import jakarta.annotation.PostConstruct;
import javax.sql.DataSource;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FlywayConfig {

    @Autowired
    private DataSource dataSource;

    // 禁止flay way自动清理（否则会自动删掉所有的表）
    @Value("${spring.flyway.clean-disabled}")
    private boolean cleanDisabled;
    // 字符编码
    @Value("${spring.flyway.encoding}")
    private String encoding;

    // 对执行迁移时基准版本的描述
    @Value("${spring.flyway.baseline-description}")
    private String baselineDescription;

    // 是否自动执行基准迁移
    @Value("${spring.flyway.baseline-on-migrate}")
    private boolean baselineOnMigrate;

    // 指定 baseline 的版本号
     @Value("${spring.flyway.baseline-version}")
     private String baselineVersion;

    // 迁移时是否校验
    @Value("${spring.flyway.validate-on-migrate}")
    private boolean validateOnMigrate;

    // 是否允许无序的迁移
    @Value("${spring.flyway.out-of-order}")
    private boolean outOfOrder;

    // 当读取元数据表时是否忽略错误的迁移
//    @Value("${spring.flyway.ignore-future-migrations}")
//    private boolean ignoreFutureMigrations;

    @Value("${spring.flyway.locations}")
    private String locations;

    @Value("${spring.flyway.placeholderReplacement}")
    private boolean placeholderReplacement;

    // 当初始化好连接时要执行的SQL
//    @Value("${spring.flyway.init-sql}")
//    private String initSql;

    @PostConstruct
    public void migrate() {
        Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .cleanDisabled(cleanDisabled)
                .encoding(encoding)
                .baselineDescription(baselineDescription)
                .baselineOnMigrate(baselineOnMigrate)
                 .baselineVersion(baselineVersion)
                .validateOnMigrate(validateOnMigrate)
                .outOfOrder(outOfOrder)
//                .ignoreFutureMigrations(ignoreFutureMigrations)
                .locations(locations)
                .placeholderReplacement(placeholderReplacement)
//                .initSql(initSql)
                .load();
        flyway.migrate();
    }

}


package com.db.core.spi.exception;

import java.sql.Connection;
import java.sql.SQLException;

import cn.hutool.core.util.StrUtil;

public class DBSQLException extends DBCException {
    private final String sqlQuery;

    public String getSqlQuery() {
        return sqlQuery;
    }

    public DBSQLException(String sqlQuery, Throwable cause, Connection connection) {
        super(cause, connection);
        this.sqlQuery = sqlQuery;
    }
}

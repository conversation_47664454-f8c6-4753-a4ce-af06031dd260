<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="5@192.168.1.136" uuid="23eb6992-4e84-45f1-95ed-737fc4949541">
      <driver-ref>redis</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/db-manage-web/db-admin/src/main/resources/application-druid.yml</remarks>
      <jdbc-driver>jdbc.RedisDriver</jdbc-driver>
      <jdbc-url>*********************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="mysql" uuid="d92cc5df-1bbc-4ae0-a6a6-63e47036a820">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/db-manage-web/db-admin/src/main/resources/application-druid.yml</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*******************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>
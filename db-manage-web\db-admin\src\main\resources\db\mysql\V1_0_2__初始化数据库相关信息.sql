
-- ----------------------------
-- Table structure for db_chart
-- ----------------------------
DROP TABLE IF EXISTS db_chart;
CREATE TABLE db_chart  (
                             id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                             gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                             gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                             name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图表名称',
                             description varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图表描述',
                             `schema` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图表信息',
                             data_source_id bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '数据源连接ID',
                             type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库类型',
                             database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                             ddl text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'ddl内容',
                             deleted text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '是否被删除,y表示删除,n表示未删除',
                             user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                             schema_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schemaName',
                             PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义报表表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_chart
-- ----------------------------
INSERT INTO db_chart VALUES (1, '2025-05-20 11:34:03', '2025-05-20 11:38:56', NULL, NULL, '{\"chartType\":\"Column\",\"xAxis\":\"name\",\"yAxis\":\"total_score\"}', 1, NULL, 'DEMO', 'SELECT s.name, sc.chinese_score, sc.math_score, sc.english_score, sc.science_score, sc.humanities_score,\n(sc.chinese_score + sc.math_score + sc.english_score + sc.science_score + sc.humanities_score) AS total_score\nFROM student s\nJOIN score sc ON s.id = sc.student_id', 'N', 1, NULL);
INSERT INTO db_chart VALUES (2, '2025-05-20 11:34:03', '2025-05-20 11:38:56', NULL, NULL, '{\"chartType\":\"Pie\",\"xAxis\":\"grade\"}', 1, NULL, 'DEMO', 'SELECT s.name,\n       score.chinese_score,\n       score.math_score,\n       score.english_score,\n       score.science_score,\n       score.humanities_score,\n       (score.chinese_score + score.math_score + score.english_score + score.science_score + score.humanities_score) AS total_score,\n       CASE\n           WHEN (score.chinese_score + score.math_score + score.english_score + score.science_score + score.humanities_score) < 630 THEN \"D\"\n           WHEN (score.chinese_score + score.math_score + score.english_score + score.science_score + score.humanities_score) >= 630 AND (score.chinese_score + score.math_score + score.english_score + score.science_score + score.humanities_score) <= 735 THEN \"C\"\n           WHEN (score.chinese_score + score.math_score + score.english_score + score.science_score + score.humanities_score) > 840 THEN \"A\"\n           ELSE \"B\"\n       END AS grade\nFROM score\nJOIN student s ON score.student_id = s.id', 'N', 1, NULL);
INSERT INTO db_chart VALUES (3, '2025-05-20 11:34:03', '2025-05-20 11:38:56', NULL, NULL, '{\"chartType\":\"Line\",\"xAxis\":\"name\",\"yAxis\":\"chinese_score\"}', 1, NULL, 'DEMO', 'SELECT s.name, sc.chinese_score, sc.math_score, sc.english_score, sc.science_score, sc.humanities_score,\n(sc.chinese_score + sc.math_score + sc.english_score + sc.science_score + sc.humanities_score) AS total_score\nFROM student s\nJOIN score sc ON s.id = sc.student_id', 'N', 1, NULL);

-- ----------------------------
-- Table structure for db_dashboard
-- ----------------------------
DROP TABLE IF EXISTS db_dashboard;
CREATE TABLE db_dashboard  (
                                 id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                 gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                 name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报表名称',
                                 description varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报表描述',
                                 `schema` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报表布局信息',
                                 deleted text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '是否被删除,y表示删除,n表示未删除',
                                 user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                                 PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义报表表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_dashboard
-- ----------------------------
INSERT INTO db_dashboard VALUES (1, '2025-05-20 11:34:03', '2025-05-20 11:38:56', '学生成绩分析', '学生成绩分析', '[[1],[2],[3]]', 'N', 1);

-- ----------------------------
-- Table structure for db_dashboard_chart_relation
-- ----------------------------
DROP TABLE IF EXISTS db_dashboard_chart_relation;
CREATE TABLE db_dashboard_chart_relation  (
                                                id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                                dashboard_id bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '报表id',
                                                chart_id bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图表id',
                                                PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义报表表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_dashboard_chart_relation
-- ----------------------------
INSERT INTO db_dashboard_chart_relation VALUES (1, '2025-05-20 11:34:03', '2025-05-20 11:34:03', 1, 1);
INSERT INTO db_dashboard_chart_relation VALUES (2, '2025-05-20 11:34:03', '2025-05-20 11:34:03', 1, 2);
INSERT INTO db_dashboard_chart_relation VALUES (3, '2025-05-20 11:34:03', '2025-05-20 11:34:03', 1, 3);

-- ----------------------------
-- Table structure for db_data_source
-- ----------------------------
DROP TABLE IF EXISTS db_data_source;
CREATE TABLE db_data_source  (
                                   id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   gmt_create datetime(0) NOT NULL COMMENT '创建时间',
                                   gmt_modified datetime(0) NOT NULL COMMENT '修改时间',
                                   alias varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '别名',
                                   url varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '连接地址',
                                   user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
                                   `password` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
                                   type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库类型',
                                   env_type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '环境类型',
                                   user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                                   host varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'host地址',
                                   port varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '端口',
                                   ssh varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ssh配置信息json',
                                   `ssl` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ssl配置信息json',
                                   sid varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sid',
                                   driver varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驱动信息',
                                   jdbc varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'jdbc版本',
                                   extend_info varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义扩展字段json',
                                   driver_config varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'driver_config配置',
                                   environment_id bigint(20) UNSIGNED NOT NULL DEFAULT 2 COMMENT '环境id',
                                   kind varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '连接类型',
                                   service_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务名',
                                   service_type varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务类型',
                                   PRIMARY KEY (id) USING BTREE,
                                   INDEX idx_user_id(user_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据源连接表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_data_source
-- ----------------------------
INSERT INTO db_data_source VALUES (2, '2025-05-19 16:59:36', '2025-05-19 16:59:36', '@*************', '*******************************/', 'root', '607Mfyb6H5fpSnDJLlRUIQ==', 'MYSQL', NULL, 1, '*************', '3306', '{\"authenticationType\":\"password\",\"hostName\":\"\",\"localPort\":\"\",\"password\":\"\",\"port\":\"22\",\"use\":false,\"userName\":\"\"}', NULL, NULL, NULL, NULL, '[{\"key\":\"zeroDateTimeBehavior\",\"required\":false,\"value\":\"convertToNull\"},{\"key\":\"useInformationSchema\",\"required\":false,\"value\":\"true\"},{\"key\":\"tinyInt1isBit\",\"required\":false,\"value\":\"false\"}]', '{\"custom\":false,\"defaultDriver\":false}', 1, 'PRIVATE', NULL, NULL);
INSERT INTO db_data_source VALUES (3, '2025-06-20 10:38:43', '2025-06-20 10:38:43', '@*************', '*********************************************', 'postgres', '607Mfyb6H5fpSnDJLlRUIQ==', 'POSTGRESQL', NULL, 1, '*************', '5433', '{\"authenticationType\":\"password\",\"hostName\":\"\",\"localPort\":\"\",\"password\":\"\",\"port\":\"22\",\"use\":false,\"userName\":\"\"}', NULL, NULL, NULL, NULL, '[]', '{\"custom\":false,\"defaultDriver\":false,\"jdbcDriver\":\"postgresql-42.5.1.jar\",\"jdbcDriverClass\":\"org.postgresql.Driver\"}', 1, 'PRIVATE', NULL, NULL);

-- ----------------------------
-- Table structure for db_data_source_access
-- ----------------------------
DROP TABLE IF EXISTS db_data_source_access;
CREATE TABLE db_data_source_access  (
                                          id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                          gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                          create_user_id bigint(20) UNSIGNED NOT NULL COMMENT '创建人用户id',
                                          modified_user_id bigint(20) UNSIGNED NOT NULL COMMENT '修改人用户id',
                                          data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源id',
                                          access_object_type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '授权类型',
                                          access_object_id bigint(20) UNSIGNED NOT NULL COMMENT '授权id,根据类型区分是用户还是团队',
                                          PRIMARY KEY (id) USING BTREE,
                                          UNIQUE INDEX uk_data_source_access(data_source_id, access_object_type, access_object_id) USING BTREE,
                                          INDEX idx_data_source_access_data_source_id(data_source_id) USING BTREE,
                                          INDEX idx_data_source_access_access_object_id(access_object_type, access_object_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据源授权' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_data_source_access
-- ----------------------------

-- ----------------------------
-- Table structure for db_environment
-- ----------------------------
DROP TABLE IF EXISTS db_environment;
CREATE TABLE db_environment  (
                                   id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                   gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                   create_user_id bigint(20) UNSIGNED NOT NULL COMMENT '创建人用户id',
                                   modified_user_id bigint(20) UNSIGNED NOT NULL COMMENT '修改人用户id',
                                   name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '环境名称',
                                   short_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '环境缩写',
                                   color varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颜色',
                                   PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据库连接环境' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_environment
-- ----------------------------
INSERT INTO db_environment VALUES (1, '2025-05-20 11:38:56', '2025-06-20 18:10:54', 1, 1, '正式环境', 'RELEASE', '#fa8c16');
INSERT INTO db_environment VALUES (2, '2025-05-20 11:38:56', '2025-06-20 10:36:31', 1, 1, '测试环境', 'TEST', 'GREEN');

-- ----------------------------
-- Table structure for db_erd_graph
-- ----------------------------
DROP TABLE IF EXISTS db_erd_graph;
CREATE TABLE db_erd_graph  (
                                 id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                 gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                 data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                 database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                 name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保存名称',
                                 type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库类型',
                                 status varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ddl语句状态:DRAFT/RELEASE',
                                 erd_json longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'ddl内容',
                                 tab_opened char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否在tab中被打开,y表示打开,n表示未打开',
                                 user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                                 db_schema_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                 operation_type varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
                                 PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 131 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '我的ER图模型' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for db_jdbc_driver
-- ----------------------------
DROP TABLE IF EXISTS db_jdbc_driver;
CREATE TABLE db_jdbc_driver  (
                                   id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                   gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                   db_type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'db类型',
                                   jdbc_driver varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'jar包',
                                   jdbc_driver_class varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'driver class类',
                                   PRIMARY KEY (id) USING BTREE,
                                   INDEX idx_db_type(db_type) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义驱动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_jdbc_driver
-- ----------------------------

-- ----------------------------
-- Table structure for db_operation_log
-- ----------------------------
DROP TABLE IF EXISTS db_operation_log;
CREATE TABLE db_operation_log  (
                                     id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                     gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                     data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                     database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                     type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库类型',
                                     ddl text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'ddl内容',
                                     user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                                     status varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'success' COMMENT '状态',
                                     operation_rows bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '操作行数',
                                     use_time bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '使用时长',
                                     extend_info varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展信息',
                                     schema_name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                     PRIMARY KEY (id) USING BTREE,
                                     INDEX idx_op_data_source_id(data_source_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 179 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '我的执行记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_operation_log
-- ----------------------------

-- ----------------------------
-- Table structure for db_operation_saved
-- ----------------------------
DROP TABLE IF EXISTS db_operation_saved;
CREATE TABLE db_operation_saved  (
                                       id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                       gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                       data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                       database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                       name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保存名称',
                                       type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库类型',
                                       status varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ddl语句状态:DRAFT/RELEASE',
                                       ddl longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'ddl内容',
                                       tab_opened text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '是否在tab中被打开,y表示打开,n表示未打开',
                                       user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                                       db_schema_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                       operation_type varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
                                       PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '我的保存表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_operation_saved
-- ----------------------------
INSERT INTO db_operation_saved VALUES (9, '2025-06-05 16:23:29', '2025-06-27 17:17:10', 2, 'db-manage', '查询用户', 'MYSQL', 'RELEASE', 'select *FROM sys_user ', 'y', 1, NULL, 'console');
INSERT INTO db_operation_saved VALUES (55, '2025-06-24 09:56:30', '2025-06-25 14:39:26', 2, 'db-manage', '查看监控状态', 'MYSQL', 'RELEASE', 'SHOW FULL PROCESSLIST;\r\nSELECT\r\n  *\r\nFROM\r\n  test\r\nWHERE\r\n  id = 1', 'n', 1, NULL, 'console');

-- ----------------------------
-- Table structure for db_pin_table
-- ----------------------------
DROP TABLE IF EXISTS db_pin_table;
CREATE TABLE db_pin_table  (
                                 id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                 gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                 data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                 database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                 schema_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                 table_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'table_name',
                                 deleted text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '是否被删除,y表示删除,n表示未删除',
                                 user_id bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
                                 PRIMARY KEY (id) USING BTREE,
                                 INDEX idx_user_id_data_source_id(user_id, data_source_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'PIN TABLES' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_pin_table
-- ----------------------------

-- ----------------------------
-- Table structure for db_system_config
-- ----------------------------
DROP TABLE IF EXISTS db_system_config;
CREATE TABLE db_system_config  (
                                     id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                     gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                     code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置项编码',
                                     content varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置项内容',
                                     summary varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置项说明',
                                     PRIMARY KEY (id) USING BTREE,
                                     UNIQUE INDEX uk_code(code) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配置中心表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_system_config
-- ----------------------------

-- ----------------------------
-- Table structure for db_table_cache
-- ----------------------------
DROP TABLE IF EXISTS db_table_cache;
CREATE TABLE db_table_cache  (
                                   id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                   gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                   data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                   database_name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                   schema_name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                   table_name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'table名称',
                                   `key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一索引',
                                   `version` bigint(20) UNSIGNED NOT NULL COMMENT '版本',
                                   `columns` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表字段',
                                   extend_info varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义扩展字段json',
                                   PRIMARY KEY (id) USING BTREE,
                                   INDEX idx_table_cache_data_source_id(data_source_id) USING BTREE,
                                   INDEX idx_table_cache_key_version(`key`, `version`) USING BTREE,
                                   INDEX idx_table_cache_key_table_name(`key`, table_name) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3529 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'table cache' ROW_FORMAT = Dynamic;
-- ----------------------------
-- Records of db_table_cache
-- ----------------------------


-- ----------------------------
-- Table structure for db_table_cache_version
-- ----------------------------
DROP TABLE IF EXISTS db_table_cache_version;
CREATE TABLE db_table_cache_version  (
                                           id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                           gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                           data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                           database_name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                           schema_name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                           `key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一索引',
                                           `version` bigint(20) UNSIGNED NOT NULL COMMENT '版本',
                                           table_count bigint(20) UNSIGNED NOT NULL COMMENT '表数量',
                                           status varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
                                           PRIMARY KEY (id) USING BTREE,
                                           UNIQUE INDEX uk_table_cache_version_key(`key`) USING BTREE,
                                           INDEX idx_table_cache_version_data_source_id(data_source_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'table cache version' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_table_cache_version
-- ----------------------------

-- ----------------------------
-- Table structure for db_table_vector_mapping
-- ----------------------------
DROP TABLE IF EXISTS db_table_vector_mapping;
CREATE TABLE db_table_vector_mapping  (
                                            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            api_key varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'api key',
                                            data_source_id bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '数据源连接ID',
                                            `database` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据库名称',
                                            `schema` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'schema名称',
                                            status varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                            PRIMARY KEY (id) USING BTREE,
                                            INDEX idx_api_key(api_key) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'milvus映射表保存记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_table_vector_mapping
-- ----------------------------

-- ----------------------------
-- Table structure for db_task
-- ----------------------------
DROP TABLE IF EXISTS db_task;
CREATE TABLE db_task  (
                            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                            gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                            gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                            data_source_id bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '数据源连接ID',
                            database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                            schema_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                            table_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'table_name',
                            deleted varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否被删除,y表示删除,n表示未删除',
                            user_id bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
                            task_type varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'task type, such as: DOWNLOAD_DATA, UPLOAD_TABLE_DATA, DOWNLOAD_TABLE_STRUCTURE, UPLOAD_TABLE_STRUCTURE,',
                            task_status varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'task status',
                            task_progress varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'task progress',
                            task_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'task name',
                            content blob NULL COMMENT 'task content',
                            download_url varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'down load url',
                            PRIMARY KEY (id) USING BTREE,
                            INDEX idx_task_user_id(user_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据导出导入任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_task
-- ----------------------------

-- ----------------------------
-- Table structure for db_team
-- ----------------------------
DROP TABLE IF EXISTS db_team;
CREATE TABLE db_team  (
                            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                            gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                            gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                            create_user_id bigint(20) UNSIGNED NOT NULL COMMENT '创建人用户id',
                            modified_user_id bigint(20) UNSIGNED NOT NULL COMMENT '修改人用户id',
                            code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '团队编码',
                            name varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '团队名称',
                            status varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'VALID' COMMENT '团队状态',
                            description text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '团队描述',
                            PRIMARY KEY (id) USING BTREE,
                            UNIQUE INDEX uk_team_code(code) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '团队' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_team
-- ----------------------------

-- ----------------------------
-- Table structure for db_team_user
-- ----------------------------
DROP TABLE IF EXISTS db_team_user;
CREATE TABLE db_team_user  (
                                 id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 gmt_create datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                 gmt_modified datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                 create_user_id bigint(20) UNSIGNED NOT NULL COMMENT '创建人用户id',
                                 modified_user_id bigint(20) UNSIGNED NOT NULL COMMENT '修改人用户id',
                                 team_id bigint(20) UNSIGNED NOT NULL COMMENT '团队id',
                                 user_id bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
                                 PRIMARY KEY (id) USING BTREE,
                                 UNIQUE INDEX uk_team_user(team_id, user_id) USING BTREE,
                                 INDEX idx_team_user_team_id(team_id) USING BTREE,
                                 INDEX idx_team_user_user_id(user_id) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户团队表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_team_user
-- ----------------------------

-- ----------------------------
-- Table structure for db_transaction_session
-- ----------------------------
DROP TABLE IF EXISTS db_transaction_session;
CREATE TABLE db_transaction_session  (
                                           id bigint(20) NOT NULL COMMENT '主键',
                                           create_time datetime(0) NOT NULL COMMENT '创建时间',
                                           update_time datetime(0) NOT NULL COMMENT '修改时间',
                                           data_source_id bigint(20) UNSIGNED NOT NULL COMMENT '数据源连接ID',
                                           database_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'db名称',
                                           db_schema_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'schema名称',
                                           type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库类型',
                                           user_id bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户id',
                                           start_time timestamp(0) NULL DEFAULT NULL COMMENT '事务开始时间',
                                           end_time timestamp(0) NULL DEFAULT NULL COMMENT '事务结束时间',
                                           isolation_level varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事务隔离级别（如READ_COMMITTED）',
                                           status int(11) NOT NULL DEFAULT 0 COMMENT '状态（0:进行中,1:已提交,2:回滚）',
                                           PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '事务会话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_transaction_session
-- ----------------------------

-- ----------------------------
-- Table structure for db_transaction_sql_operation
-- ----------------------------
DROP TABLE IF EXISTS db_transaction_sql_operation;
CREATE TABLE db_transaction_sql_operation  (
                                                 id bigint(20) NOT NULL COMMENT '主键',
                                                 session_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事务会话ID',
                                                 sql_text text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行的SQL语句',
                                                 operation_type varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型（SELECT, INSERT, UPDATE, DELETE）',
                                                 executed_at timestamp(0) NULL DEFAULT NULL COMMENT '执行时间',
                                                 affected_rows int(255) NULL DEFAULT NULL COMMENT '影响行数',
                                                 success int(1) NULL DEFAULT NULL COMMENT '是否执行成功 0否，1是',
                                                 result_msg text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '错误信息（失败时记录）',
                                                 PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'SQL操作记录表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_transaction_sql_operation
-- ----------------------------

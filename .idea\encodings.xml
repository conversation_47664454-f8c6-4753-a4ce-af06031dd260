<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/db-manage-web/db-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-framework/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-framework/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-generator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-generator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-core-spi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-core-spi/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-server-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-server-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-server-domain/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-server-domain/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-server-tools/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/db-server-tools/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-model/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-dm/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-dm/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-h2/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-h2/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-kingbase/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-kingbase/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-mysql/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-mysql/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-oceanbase/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-oceanbase/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-oracle/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-oracle/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-postgresql/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-postgresql/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-sqlite/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-sqlite/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-sqlserver/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/db-sqlserver/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-plugins/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-quartz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-quartz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/db-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/db-manage-web/src/main/resources" charset="UTF-8" />
  </component>
</project>
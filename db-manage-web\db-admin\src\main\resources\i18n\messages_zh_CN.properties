common.businessError=请尝试重新提交或者刷新页面
common.systemError=系统发生异常，可在帮助中点击日志查看异常详情。
common.needLoggedIn=需要登陆页面
common.redirect=重定向页面
common.paramError=您输入的参数异常
common.paramDetailError=您输入的参数:{0},存在异常
common.paramCheckError=请检查以下参数:
common.maxUploadSize=您输入的文件超过最大限制
common.permissionDenied=您没有权限访问该页面
common.dataNotFound=您访问的数据不存在
common.dataAlreadyExists=数据库总已经存在该数据
common.dataAlreadyExistsWithParam=数据库总已经存在该数据,{0}:{1}

oauth.userNameNotExits=当前账号不存在
oauth.IllegalUserName=当前账号无法登录，请换一个账号
oauth.passwordIncorrect=您输入的密码有误
oauth.invalidUserName=您输入的账号已经失效

dataSource.sqlAnalysisError=不合法的执行语句
connection.error=数据库链接异常，请检查数据库配置
connection.ssh.error=SSH 链接异常，请检查SSH配置
connection.driver.load.error=数据库驱动加载异常，请检查驱动配置
# sqlResult
sqlResult.rowNumber=行号
sqlResult.success=执行成功

user.canNotOperateSystemAccount=不能操作系统账号
execute.exportCsv=更多数据请点击导出csv

settings.permissionDeniedForAiConfig=请联系管理员在 “设置->自定义AI” 里面设置ApiKey
main.indexName=名称
main.indexFieldName=字段
main.indexType=索引类型
main.indexMethod=索引方法
main.indexNote=注释
main.fieldNo=序号
main.fieldName=字段名
main.fieldType=数据类型
main.fieldLength=长度
main.fieldIfEmpty=不是null
main.fieldDefault=默认值
main.fieldDecimalPlaces=小数位
main.fieldNote=备注
main.databaseText=数据库：
main.sheetName=表结构

#错误消息
not.null=* 必须填写
user.jcaptcha.error=验证码错误
user.jcaptcha.expire=验证码已失效
user.not.exists=用户不存在/密码错误
user.password.not.match=用户不存在/密码错误
user.password.retry.limit.count=密码输入错误{0}次
user.password.retry.limit.exceed=密码输入错误{0}次，帐户锁定{1}分钟
user.password.delete=对不起，您的账号已被删除
user.blocked=用户已封禁，请联系管理员
role.blocked=角色已封禁，请联系管理员
login.blocked=很遗憾，访问IP已被列入系统黑名单
user.logout.success=退出成功

length.not.valid=长度必须在{min}到{max}个字符之间

user.username.not.valid=* 2到20个汉字、字母、数字或下划线组成，且必须以非数字开头
user.password.not.valid=* 5-50个字符

user.email.not.valid=邮箱格式错误
user.mobile.phone.number.not.valid=手机号格式错误
user.login.success=登录成功
user.register.success=注册成功
user.notfound=请重新登录
user.forcelogout=管理员强制退出，请重新登录
user.unknown.error=未知错误，请重新登录

##文件上传消息
upload.exceed.maxSize=上传的文件大小超出限制的文件大小！<br/>允许的文件最大大小是：{0}MB！
upload.filename.exceed.length=上传的文件名最长{0}个字符

##权限
no.permission=您没有数据的权限，请联系管理员添加权限 [{0}]
no.create.permission=您没有创建数据的权限，请联系管理员添加权限 [{0}]
no.update.permission=您没有修改数据的权限，请联系管理员添加权限 [{0}]
no.delete.permission=您没有删除数据的权限，请联系管理员添加权限 [{0}]
no.export.permission=您没有导出数据的权限，请联系管理员添加权限 [{0}]
no.view.permission=您没有查看数据的权限，请联系管理员添加权限 [{0}]


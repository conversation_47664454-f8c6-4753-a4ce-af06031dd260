
package com.db.server.admin.api.controller.common.request;

import com.db.server.dom.api.enums.AccessObjectTypeEnum;
import com.db.server.tools.base.wrapper.request.PageQueryRequest;
import lombok.Data;

/**
 * Common pagination query
 *
 * <AUTHOR>
 */
@Data
public class TeamUserPageQueryRequest extends PageQueryRequest {

    /**
     * Authorization type
     *
     * @see AccessObjectTypeEnum
     */
    private String type;

    /**
     * searchKey
     */
    private String searchKey;
}

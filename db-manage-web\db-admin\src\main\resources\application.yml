# 项目相关配置
db:
  # 名称
  name: 数据库管理开发工具
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/db/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/db/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.db: debug
    org.flywaydb: DEBUG
#    org.mybatis: DEBUG
    org.springframework: WARN
    org.springdoc: DEBUG
#    org.mapstruct: DEBUG
# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 120MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
      # 排除的路径，这些路径下的文件变化不会触发重启
      exclude: static/**,public/**,templates/**,META-INF/**
      # 额外的路径，这些路径下的文件变化会触发重启
      additional-paths: src/main/java,src/main/resources
      # 轮询间隔（毫秒）
      poll-interval: 1000
      # 静默期（毫秒），避免频繁重启
      quiet-period: 400
      # 触发文件
      trigger-file: .trigger
    livereload:
      # 启用LiveReload
      enabled: true
      # LiveReload端口
      port: 35729
  # Spring Boot 默认提供了对 Flyway 的自动配置，只需要在这里配置好参数即可自动启用。
  flyway:
    enabled: true # 默认启用脚本自动升级
    encoding: utf-8 # 字符编码
    clean-disabled: true # 禁止flay way自动清理（否则会自动删掉所有的表）
#    ignore-missing-migrations: true # 允许忽略缺失的迁移脚本，不会因为缺少某些版本的脚本而报错
    baseline-description: "<< Flyway Baseline >>" #执行迁移时对基准版本的描述
    out-of-order: true # 是否允许不按顺序迁移 开发建议 true  生产建议 false
    locations: classpath:db/mysql # sql存放目录，多个的话以，隔开。像我这样写的话，就会默认读取ruoyi-admin模块下的resource目录中的sql目录下的mysql文件夹中的sql文件。
    table: flyway_schema_history
    # 到新的环境中数据库中有数据，且没有flyway_schema_history表时，是否执行迁移操作,
    # 若是设置为false，在启动时会报错，并中止迁移;若是为true,则生成history表并完成全部的迁移，要根据实际状况设置;
    baseline-on-migrate: true
    validate-on-migrate: true  # 在迁移时，是否校验脚本，假设V1.0__初始.sql已经迁移过了，在下次启动时会校验该脚本是否有变动过，则抛出异常
    baseline-version: 1.0.0 # 这样可以忽略 202501081120 版本以及之前的所有 migration，这里你可以自己配置
    placeholderReplacement: false # 是否启用占位符替换，如果启用替换，遇到&{xxx}形式的SQL内容，可能会发生报错
    # 当初始化好连接时要执行的SQL
#    init-sql: SELECT * FROM pg_tables WHERE tablename NOT LIKE'pg%' AND tablename NOT LIKE'sql_%' ORDER BY tablename;

  # Kafka配置
  kafka:
    # Kafka服务器地址
    bootstrap-servers: 192.168.1.136:9092
    # 生产者配置
    producer:
      # 确认模式：all表示所有副本都确认
      acks: all
      # 重试次数
      retries: 3
      # 批次大小
      batch-size: 16384
      # 缓冲区内存
      buffer-memory: 33554432
      # 延迟时间（毫秒）
      linger-ms: 1
      # 压缩类型
      compression-type: snappy
      # 键序列化器
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值序列化器
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 请求超时时间
      request-timeout-ms: 30000
      # 最大阻塞时间
      max-block-ms: 60000
    # 消费者配置
    consumer:
      # 消费者组ID
      group-id: db-manage-group
      # 自动偏移量重置策略
      auto-offset-reset: earliest
      # 是否自动提交偏移量
      enable-auto-commit: false
      # 键反序列化器
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值反序列化器
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 会话超时时间
      session-timeout-ms: 30000
      # 心跳间隔时间
      heartbeat-interval-ms: 10000
      # 最大拉取记录数
      max-poll-records: 500
      # 拉取超时时间
      poll-timeout-ms: 3000
      # 最大拉取间隔
      max-poll-interval-ms: 300000
    # 监听器配置
    listener:
      # 确认模式
      ack-mode: manual_immediate
      # 并发数
      concurrency: 3
      # 拉取超时时间
      poll-timeout: 3000
    # 主题配置
    topic:
      # 默认分区数
      partitions: 3
      # 默认副本数
      replication-factor: 1
      # 消息保留时间永久
      retention-ms: -1
      # 清理策略
      cleanup-policy: delete
      # 压缩类型
      compression-type: snappy
      # 最大消息大小
      max-message-bytes: 1000000

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        # 暴露健康检查端点
        include: health,info,kafka
  endpoint:
    health:
      # 显示详细健康信息
      show-details: always
      # 显示组件信息
      show-components: always
  health:
    # 启用Kafka健康检查
    kafka:
      enabled: true
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis Plus配置
mybatis-plus:
  # Mapper.xml 文件位置 Maven 多模块项目的扫描路径需以 classpath*: 开头
  mapper-locations: classpath*:mapper/**/*.xml
  #  #MyBaits 别名包扫描路径，通过该属性可以给包中的类注册别名 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.db.**.domain
  checkConfigLocation : true
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

  global-config: # 全局策略配置
    # 是否控制台 print mybatis-plus 的 LOGO
    banner: true
    db-config:
      # 根据数据库类型设置转义符模板
      column-format:
        mysql: "`%s`"       # MySQL使用反引号
        sqlserver: "[%s]"   # SQL Server用方括号
        postgresql: "\"%s\"" # PostgreSQL双引号
      # id类型
      id-type: auto
      # 表名是否使用下划线命名，默认数据库表使用下划线命名
      table-underline: true
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
#knife4j配置
knife4j:
  #启用
  enable: true
  production: false
  #基础认证
  basic:
    enable: false
    username: ruoyi
    password: 123456
  #增强配置
  setting:
    swagger-model-name: 实体类列表
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh_cn
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2023 XXX All Rights Reserved

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/api/**
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
minidao :
  base-package: org.jeecg.modules.jmreport.desreport.dao*
  db-type: mysql


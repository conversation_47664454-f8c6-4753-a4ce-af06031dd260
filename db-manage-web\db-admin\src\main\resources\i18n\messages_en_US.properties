common.businessError=Please try resubmitting or refreshing the page later
common.systemError=An exception occurs, you can view the exception details in the log in the help menu.
common.needLoggedIn=Login required
common.redirect=Redirect
common.paramError=The parameter is incorrect
common.paramDetailError=The parameter: {0} is incorrect
common.paramCheckError=The following parameters are not valid
common.maxUploadSize=The file exceeds the maximum limit
common.permissionDenied=Permission denied
common.dataNotFound=Data not found
common.dataAlreadyExists=The data already exists in the database
common.dataAlreadyExistsWithParam=The data already exists in the database,{0}:{1}

oauth.userNameNotExits=The current account does not exist
oauth.IllegalUserName=The current account cannot be logged in. Please change your account
oauth.passwordError=The password you entered is incorrect
oauth.invalidUserName=The current account is invalid


dataSource.sqlAnalysisError=Invalid statements
connection.error=Connection failed, please check the connection information
connection.ssh.error=SSH connection failed, please check the connection information
connection.driver.load.error=Failed to load driver class, please check the driver jar package
# sqlResult
sqlResult.rowNumber=Row Number
sqlResult.success=Execution successful

user.canNotOperateSystemAccount=System accounts cannot be operated
execute.exportCsv=For more data, please click on Export CSV

settings.permissionDeniedForAiConfig=Please contact the administrator to set ApiKey in "Settings ->Custom Ai"
main.indexName=Name
main.indexFieldName=Column Name
main.indexType=Index Type
main.indexMethod=Index Method
main.indexNote=Index Comment
main.fieldNo=No
main.fieldName=Field Name
main.fieldType=Column Type
main.fieldLength=Length
main.fieldIfEmpty=Nullable
main.fieldDefault=Column Default
main.fieldDecimalPlaces=Decimal Places
main.fieldNote=Column Comment
main.databaseText=Database:
main.sheetName=Table Structure
